# OSGEarth 高程测试说明

## 概述
本目录包含专门用于测试OSGEarth高程功能的简化配置和测试工具。

## 测试文件说明

### 1. 应用程序
- **`osgearth_map_simple.exe`** - 简化版OSGEarth查看器，专门用于高程测试

### 2. 配置文件

#### `minimal_elevation.earth` - 最基础配置
- **用途**: 测试最基本的椭球面高程
- **特点**: 
  - 使用WGS84椭球面作为高程参考
  - 海平面高程应为0米
  - 不依赖网络连接
  - 纯色背景，便于观察
- **适用场景**: 排除所有外部因素，验证基础高程功能

#### `elevation_test.earth` - 高程可视化配置
- **用途**: 将高程数据转换为颜色显示
- **特点**:
  - 蓝色表示低海拔（-1000米以下）
  - 绿色表示海平面（0米）
  - 黄色表示中等海拔（1000米）
  - 红色表示高海拔（3000米以上）
- **适用场景**: 直观观察高程变化

#### `main.earth` - 完整配置
- **用途**: 包含Google卫星图像的完整配置
- **特点**: 真实卫星图像 + 高程数据
- **适用场景**: 实际应用场景测试

## 快速开始

### 方法1: 使用批处理脚本（推荐）
```bash
# 双击运行
test_elevation.bat

# 或命令行指定配置
test_elevation.bat 1  # 最简配置
test_elevation.bat 2  # 高程可视化
test_elevation.bat 3  # 完整配置
```

### 方法2: 直接运行
```bash
# 测试最简配置
osgearth_map_simple.exe minimal_elevation.earth

# 测试高程可视化
osgearth_map_simple.exe elevation_test.earth

# 测试完整配置
osgearth_map_simple.exe main.earth
```

## 操作指南

### 鼠标操作
- **左键拖拽**: 平移地图
- **右键拖拽**: 缩放
- **中键拖拽**: 旋转视角
- **滚轮**: 缩放
- **左键单击**: 拾取坐标和高程

### 键盘快捷键
- **h**: 显示帮助信息
- **e**: 检查高程图层状态
- **ESC**: 退出程序

## 测试步骤

### 第一步: 基础功能验证
1. 运行 `test_elevation.bat 1` 或直接运行最简配置
2. 观察程序是否正常启动
3. 检查控制台是否有错误信息
4. 按 `e` 键检查高程图层状态

### 第二步: 高程数据验证
1. 在地球表面左键单击
2. 观察控制台输出的坐标信息
3. **重点检查**: `Elev MSL` 值是否合理
   - 海平面附近应接近0米
   - 不应该出现大幅负值（如-7000米等）

### 第三步: 图层状态检查
1. 按 `e` 键查看图层信息
2. 确认高程图层显示：
   - `status: OK`
   - `open: YES` 
   - `visible: YES`

## 预期结果

### 正常情况
```
Pick Result -> Lat: 40.000000°, Lon: -100.000000°, Elev MSL: 0.0m, Alt above ellipsoid: 1.2m
[Elevation Test] Found 1 elevation layer(s)
[Elevation Test] Layer #1: ellipsoid (status: OK) (open: YES) (visible: YES)
```

### 异常情况
```
Pick Result -> Lat: 40.000000°, Lon: -100.000000°, Elev MSL: -7677.2m, Alt above ellipsoid: -7311.8m
[Elevation Test] WARNING: No elevation layers found!
```

## 故障排除

### 问题1: 程序无法启动
- **检查**: 确认所有DLL文件都在同一目录
- **解决**: 重新复制完整的redist_desk目录

### 问题2: 高程值为大幅负值
- **原因**: 椭球面高程驱动器可能不工作
- **解决**: 
  1. 检查OSGEarth版本兼容性
  2. 尝试使用其他高程数据源
  3. 检查坐标系统配置

### 问题3: 找不到高程图层
- **检查**: 配置文件是否正确加载
- **解决**: 确认earth文件语法正确，检查控制台错误信息

### 问题4: 网络相关错误
- **说明**: minimal_elevation.earth不依赖网络
- **解决**: 如果仍有网络错误，检查代理设置

## 技术说明

### 椭球面高程 vs 海平面高程
- **椭球面高程**: 相对于WGS84椭球面的高度
- **海平面高程**: 相对于大地水准面的高度
- **关系**: 两者可能有几米到几十米的差异

### 坐标系统
- **地图坐标系**: OSGEarth内部使用的投影坐标
- **地理坐标系**: 经纬度坐标系（WGS84）
- **转换**: 程序自动处理坐标系统转换

## 联系信息
如果测试过程中遇到问题，请：
1. 记录控制台完整输出
2. 说明使用的配置文件
3. 描述具体的操作步骤和现象 