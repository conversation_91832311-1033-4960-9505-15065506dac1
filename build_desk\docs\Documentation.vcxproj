﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{01B733E9-47EE-32E0-9397-7F84F40A6F0D}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Documentation</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/docs/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/docs/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/docs/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/docs/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/docs/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/docs/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/docs/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/docs/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\3.0_upgrade_guide.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\TileLayer.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\arcgis.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\bing.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\build.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\cesium_native.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\cesiumion.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\composite.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\contourmap.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\data.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\earthfile.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\elevation.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\envvars.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\faq.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\featureimage.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\featuremodel.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\gdal.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\image.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\index.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\install.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\layer.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\layers.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\license.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\mapboxgl.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\mbtiles.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\osgearth_conv.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\releasenotes.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\support.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\tiledfeaturemodel.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\tms.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\visiblelayer.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\wms.md">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\source\xyz.md">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\ZERO_CHECK.vcxproj">
      <Project>{4A2C7910-819B-3DF4-9EF6-60A9CC2F4215}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>