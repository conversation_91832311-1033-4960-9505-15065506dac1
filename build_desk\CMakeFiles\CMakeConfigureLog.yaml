
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:204 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The system is: Windows - 10.0.26058 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/7/7 17:55:08銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      VcpkgCheckManifestRoot:
        The vcpkg manifest was disabled, but we found a manifest file in F:\\cmo-dev\\my_osgearth_web\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:06.15
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/3.26.4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/7/7 17:55:16銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\dev\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      VcpkgCheckManifestRoot:
        The vcpkg manifest was disabled, but we found a manifest file in F:\\cmo-dev\\my_osgearth_web\\. You may want to enable vcpkg manifests in your properties page or pass /p:VcpkgEnableManifest=true to the msbuild invocation.
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /I"C:\\dev\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\dev\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\dev\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.exe" "C:\\dev\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdC.tlog\\CompilerIdC.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\3.26.4\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:03.84
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/3.26.4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-7q7eu1"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-7q7eu1"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-7q7eu1
        
        Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_b4613.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/7/7 17:55:20銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-7q7eu1\\cmTC_b4613.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b4613.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-7q7eu1\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_b4613.dir\\Debug\\cmTC_b4613.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_b4613.dir\\Debug\\cmTC_b4613.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_b4613.dir\\Debug\\cmTC_b4613.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_b4613.dir\\Debug\\\\" /Fd"cmTC_b4613.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_b4613.dir\\Debug\\\\" /Fd"cmTC_b4613.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-7q7eu1\\Debug\\cmTC_b4613.exe" /INCREMENTAL /ILK:"cmTC_b4613.dir\\Debug\\cmTC_b4613.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-7q7eu1/Debug/cmTC_b4613.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-7q7eu1/Debug/cmTC_b4613.lib" /MACHINE:X64  /machine:x64 cmTC_b4613.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_b4613.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-7q7eu1\\Debug\\cmTC_b4613.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_b4613.dir\\Debug\\cmTC_b4613.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_b4613.dir\\Debug\\cmTC_b4613.tlog\\cmTC_b4613.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-7q7eu1\\cmTC_b4613.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.39
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-iksnvd"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-iksnvd"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      VCPKG_INSTALLED_DIR: "C:/dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-iksnvd
        
        Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_3ceb4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/7/7 17:55:23銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-iksnvd\\cmTC_3ceb4.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_3ceb4.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-iksnvd\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_3ceb4.dir\\Debug\\cmTC_3ceb4.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_3ceb4.dir\\Debug\\cmTC_3ceb4.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_3ceb4.dir\\Debug\\cmTC_3ceb4.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3ceb4.dir\\Debug\\\\" /Fd"cmTC_3ceb4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3ceb4.dir\\Debug\\\\" /Fd"cmTC_3ceb4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.26\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-iksnvd\\Debug\\cmTC_3ceb4.exe" /INCREMENTAL /ILK:"cmTC_3ceb4.dir\\Debug\\cmTC_3ceb4.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-iksnvd/Debug/cmTC_3ceb4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-iksnvd/Debug/cmTC_3ceb4.lib" /MACHINE:X64  /machine:x64 cmTC_3ceb4.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_3ceb4.vcxproj -> F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-iksnvd\\Debug\\cmTC_3ceb4.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_3ceb4.dir\\Debug\\cmTC_3ceb4.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_3ceb4.dir\\Debug\\cmTC_3ceb4.tlog\\cmTC_3ceb4.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-iksnvd\\cmTC_3ceb4.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.58
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/dev/vcpkg/installed/x64-windows/share/WebP/WebPConfig.cmake:33 (find_dependency)"
      - "C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "src/osgEarthDrivers/webp/CMakeLists.txt:1 (find_package)"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-yyjwie"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-yyjwie"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: ";F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/cmake"
      VCPKG_INSTALLED_DIR: "C:/dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-yyjwie
        
        Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_f15aa.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/7/7 17:55:30銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\cmTC_f15aa.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f15aa.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_f15aa.dir\\Debug\\cmTC_f15aa.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_f15aa.dir\\Debug\\cmTC_f15aa.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_f15aa.dir\\Debug\\cmTC_f15aa.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f15aa.dir\\Debug\\\\" /Fd"cmTC_f15aa.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_f15aa.dir\\Debug\\\\" /Fd"cmTC_f15aa.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\src.c"
          src.c
        F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\cmTC_f15aa.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\cmTC_f15aa.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\cmTC_f15aa.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-yyjwie\\cmTC_f15aa.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.84
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckLibraryExists.cmake:71 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/dev/vcpkg/installed/x64-windows/share/WebP/WebPConfig.cmake:33 (find_dependency)"
      - "C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "src/osgEarthDrivers/webp/CMakeLists.txt:1 (find_package)"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-weqqwi"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-weqqwi"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: ";F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/cmake"
      VCPKG_INSTALLED_DIR: "C:/dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-weqqwi
        
        Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_3cdea.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/7/7 17:55:32銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\cmTC_3cdea.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_3cdea.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_3cdea.dir\\Debug\\cmTC_3cdea.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_3cdea.dir\\Debug\\cmTC_3cdea.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_3cdea.dir\\Debug\\cmTC_3cdea.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3cdea.dir\\Debug\\\\" /Fd"cmTC_3cdea.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_3cdea.dir\\Debug\\\\" /Fd"cmTC_3cdea.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\Debug\\cmTC_3cdea.exe" /INCREMENTAL /ILK:"cmTC_3cdea.dir\\Debug\\cmTC_3cdea.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-weqqwi/Debug/cmTC_3cdea.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-weqqwi/Debug/cmTC_3cdea.lib" /MACHINE:X64  /machine:x64 cmTC_3cdea.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\cmTC_3cdea.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\cmTC_3cdea.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\cmTC_3cdea.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-weqqwi\\cmTC_3cdea.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.42
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckLibraryExists.cmake:71 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/dev/vcpkg/installed/x64-windows/share/WebP/WebPConfig.cmake:33 (find_dependency)"
      - "C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "src/osgEarthDrivers/webp/CMakeLists.txt:1 (find_package)"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-x2c691"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-x2c691"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: ";F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/cmake"
      VCPKG_INSTALLED_DIR: "C:/dev/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/dev/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-x2c691
        
        Run Build Command(s):C:/Program Files/Microsoft Visual Studio/2022/Enterprise/MSBuild/Current/Bin/amd64/MSBuild.exe cmTC_74dcf.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n && 閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/7/7 17:55:34銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\cmTC_74dcf.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_74dcf.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_74dcf.dir\\Debug\\cmTC_74dcf.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_74dcf.dir\\Debug\\cmTC_74dcf.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_74dcf.dir\\Debug\\cmTC_74dcf.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_74dcf.dir\\Debug\\\\" /Fd"cmTC_74dcf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /RTC1 /MDd /GS /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_74dcf.dir\\Debug\\\\" /Fd"cmTC_74dcf.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\Debug\\cmTC_74dcf.exe" /INCREMENTAL /ILK:"cmTC_74dcf.dir\\Debug\\cmTC_74dcf.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-x2c691/Debug/cmTC_74dcf.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/CMakeFiles/CMakeScratch/TryCompile-x2c691/Debug/cmTC_74dcf.lib" /MACHINE:X64  /machine:x64 cmTC_74dcf.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\cmTC_74dcf.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\cmTC_74dcf.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淔:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\cmTC_74dcf.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[F:\\cmo-dev\\my_osgearth_web\\osgearth_simple2\\osgearth\\build_desk\\CMakeFiles\\CMakeScratch\\TryCompile-x2c691\\cmTC_74dcf.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.40
        
      exitCode: 1
...
