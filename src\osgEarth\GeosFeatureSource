/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/FeatureSource>
#include <osgEarth/GEOS>
#include <queue>
#include <thread>

namespace osgEarth
{
    /**
     * Feature Layer that accesses features via GEOS library for vector data processing.
     * Supports Shapefile and GeoJSON formats without GDAL dependency.
     */
    class OSGEARTH_EXPORT GeosFeatureSource : public FeatureSource
    {   
    public: // serialization
        class OSGEARTH_EXPORT Options : public FeatureSource::Options
        {
        public:
            META_LayerOptions(osgEarth, Options, FeatureSource::Options);
            OE_OPTION(URI, url);
            OE_OPTION(std::string, connection);
            OE_OPTION(std::string, format); // "shapefile" or "geojson"
            OE_OPTION(bool, buildSpatialIndex);
            OE_OPTION(bool, forceRebuildSpatialIndex);
            OE_OPTION(Config, geometryConfig);
            OE_OPTION(URI, geometryUrl);
            OE_OPTION(std::string, layer);
            OE_OPTION(Query, query);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, GeosFeatureSource, Options, FeatureSource, GeosFeatures);

        //! Location of the data resource
        void setURL(const URI& value);
        const URI& getURL() const;

        //! Database connection string (alterative to URL)
        void setConnection(const std::string& value);
        const std::string& getConnection() const;

        //! File format (shapefile or geojson)
        void setFormat(const std::string& value);
        const std::string& getFormat() const;

        //! Whether to build a spatial index after opening the resource (if supported)
        void setBuildSpatialIndex(const bool& value);
        const bool& getBuildSpatialIndex() const;

        //! Geometry configuration for explicit geometry
        void setGeometryConfig(const Config& value);
        const Config& getGeometryConfig() const;

        //! URL to geometry data
        void setGeometryUrl(const URI& value);
        const URI& getGeometryUrl() const;

        //! Layer name (for multi-layer formats)
        void setLayer(const std::string& value);
        const std::string& getLayer() const;

        //! Query to apply to the data
        void setQuery(const Query& value);
        const Query& getQuery() const;

        //! Explicit geometry to use instead of loading from URL
        void setGeometry(Geometry* geom);
        const Geometry* getGeometry() const;

    public: // Layer

        Status openImplementation() override;

        Status closeImplementation() override;

    public: // FeatureSource

        FeatureCursor* createFeatureCursorImplementation(const Query& query, ProgressCallback* progress) const override;

        bool deleteFeature(FeatureID fid) override;

        int getFeatureCount() const override;

        bool supportsGetFeature() const override;

        Feature* getFeature( FeatureID fid ) override;

        bool isWritable() const override;

        const FeatureSchema& getSchema() const override;
    
        bool insertFeature(Feature* feature) override;

        osgEarth::Geometry::Type getGeometryType() const override;

        const Status& create(
                const FeatureProfile* profile,
                const FeatureSchema& schema,
                const Geometry::Type& geometryType,
                const osgDB::Options* readOptions);

        virtual void buildSpatialIndex();

        //! Call this if the underlying geometry changes and we need to
        //! recompute the profile.
        void dirty() override;

    protected:

        virtual ~GeosFeatureSource();

        // parses an explicit WKT geometry string into a Geometry.
        Geometry* parseGeometry( const Config& geomConf );

        // read the WKT geometry from a URL, then parse into a Geometry.
        Geometry* parseGeometryUrl( const URI& geomUrl, const osgDB::Options* dbOptions );

        void initSchema();

        // Load shapefile data
        bool loadShapefile(const std::string& filename);

        // Load GeoJSON data
        bool loadGeoJSON(const std::string& filename);

        // Parse GeoJSON string
        bool parseGeoJSONString(const std::string& jsonData);

        // Convert GEOS geometry to osgEarth geometry
        Geometry* convertGeosGeometry(const void* geosGeom);

        // Convert osgEarth geometry to GEOS geometry
        void* convertToGeosGeometry(const Geometry* geom);

    private:
        osg::ref_ptr<const Profile> _profile;
        osg::ref_ptr<const Geometry> _geometry; // explicit geometry.
        std::string _source;
        std::vector<osg::ref_ptr<Feature>> _features; // In-memory feature storage
        std::thread::id _loadThreadId;
        int _featureCount;
        bool _needsSync;
        bool _writable;
        FeatureSchema _schema;
        Geometry::Type _geometryType;
        void* _geosContext; // GEOS context handle
    };

    namespace Geos
    {
        //! Internal class - do not use directly
        class GeosFeatureCursor : public FeatureCursor
        {
        public:
            //! Create a feature cursor that can query data from a feature vector.
            GeosFeatureCursor(
                const std::vector<osg::ref_ptr<Feature>>& features,
                const FeatureSource*      source,
                const FeatureProfile*     profile,
                const Query&              query,
                const FeatureFilterChain& filters,
                bool                      rewindPolygons,
                ProgressCallback*         progress
                );

        public: // FeatureCursor

            bool hasMore() const override;
            Feature* nextFeature() override;

        protected:
            virtual ~GeosFeatureCursor();

        private:
            std::vector<osg::ref_ptr<Feature>>::const_iterator _current;
            std::vector<osg::ref_ptr<Feature>>::const_iterator _end;
            Query _query;
            osg::ref_ptr<const FeatureSource> _source;
            osg::ref_ptr<const FeatureProfile> _profile;
            std::queue< osg::ref_ptr<Feature> > _queue;
            osg::ref_ptr<Feature> _lastFeatureReturned;
            const FeatureFilterChain _filters;
            bool _rewindPolygons = true;

        private:
            void readChunk();
            bool matchesQuery(const Feature* feature) const;
        };
    }

} // namespace osgEarth

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::GeosFeatureSource::Options);
