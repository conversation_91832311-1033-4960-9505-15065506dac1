<map name="Test Map" type="geocentric">
    
    <!-- Google Maps satellite imagery -->
    <image name="Google Satellite" driver="xyz">
        <url>https://mt1.google.com/vt/lyrs=s&amp;x={x}&amp;y={y}&amp;z={z}</url>
        <format>jpg</format>
        <profile>spherical-mercator</profile>
    </image>
    
    <!-- AWS Terrarium elevation data -->
    <elevation name="AWS Terrarium" driver="xyz">
        <url>https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png</url>
        <format>png</format>
        <profile>spherical-mercator</profile>
        <vdatum>egm96</vdatum>
    </elevation>
    
    <!-- Test vector data using GEOS feature source -->
    <model name="Test Features" driver="feature_geom">
        <features name="test_features" driver="geosfeatures">
            <url>../data/world.shp</url>
            <format>shapefile</format>
        </features>
        <styles>
            <style type="text/css">
                default {
                    stroke: #ffff00;
                    stroke-width: 2px;
                    fill: #ffffff40;
                }
            </style>
        </styles>
    </model>
    
</map>
