/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/Geometry>
#include <string>
#include <vector>
#include <fstream>

namespace osgEarth
{
    /**
     * Simple Shapefile reader that doesn't depend on GDAL.
     * Supports basic shapefile reading for Point, Polyline, and Polygon types.
     */
    class OSGEARTH_EXPORT ShapefileReader
    {
    public:
        enum ShapeType
        {
            SHAPE_NULL = 0,
            SHAPE_POINT = 1,
            SHAPE_POLYLINE = 3,
            SHAPE_POLYGON = 5,
            SHAPE_MULTIPOINT = 8,
            SHAPE_POINTZ = 11,
            SHAPE_POLYLINEZ = 13,
            SHAPE_POLYGONZ = 15,
            SHAPE_MULTIPOINTZ = 18,
            SHAPE_POINTM = 21,
            SHAPE_POLYLINEM = 23,
            SHAPE_POLYGONM = 25,
            SHAPE_MULTIPOINTM = 28,
            SHAPE_MULTIPATCH = 31
        };

        struct ShapeHeader
        {
            int fileCode;
            int fileLength;
            int version;
            int shapeType;
            double xMin, yMin, xMax, yMax;
            double zMin, zMax;
            double mMin, mMax;
        };

        struct RecordHeader
        {
            int recordNumber;
            int contentLength;
        };

    public:
        ShapefileReader();
        ~ShapefileReader();

        /**
         * Open a shapefile for reading
         */
        bool open(const std::string& filename);

        /**
         * Close the shapefile
         */
        void close();

        /**
         * Read all features from the shapefile
         */
        bool readFeatures(std::vector<osg::ref_ptr<Feature>>& features);

        /**
         * Get the shapefile header
         */
        const ShapeHeader& getHeader() const { return _header; }

        /**
         * Get the bounding box
         */
        void getBounds(double& xMin, double& yMin, double& xMax, double& yMax) const;

    private:
        bool readHeader();
        bool readRecord(osg::ref_ptr<Feature>& feature);
        Geometry* readPointGeometry();
        Geometry* readPolylineGeometry();
        Geometry* readPolygonGeometry();
        
        // Utility functions for reading binary data
        int readInt32();
        double readDouble();
        void readBytes(char* buffer, int count);
        bool seekTo(std::streampos pos);

        // Endian conversion
        int swapEndian32(int value);
        double swapEndianDouble(double value);

    private:
        std::ifstream _file;
        ShapeHeader _header;
        bool _isOpen;
        std::streampos _currentPos;
    };

    /**
     * Simple DBF (dBase) file reader for shapefile attributes
     */
    class OSGEARTH_EXPORT DBFReader
    {
    public:
        struct FieldDescriptor
        {
            char name[11];
            char type;
            unsigned char length;
            unsigned char decimals;
        };

        struct DBFHeader
        {
            unsigned char version;
            unsigned char year;
            unsigned char month;
            unsigned char day;
            unsigned int recordCount;
            unsigned short headerLength;
            unsigned short recordLength;
        };

    public:
        DBFReader();
        ~DBFReader();

        bool open(const std::string& filename);
        void close();
        bool readRecord(int recordIndex, AttributeTable& attributes);
        int getRecordCount() const { return _header.recordCount; }
        const std::vector<FieldDescriptor>& getFields() const { return _fields; }

    private:
        bool readHeader();
        std::string readString(int length);
        int readInt();
        double readDouble();

    private:
        std::ifstream _file;
        DBFHeader _header;
        std::vector<FieldDescriptor> _fields;
        bool _isOpen;
    };

} // namespace osgEarth
