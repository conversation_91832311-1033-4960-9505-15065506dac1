﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\CreateTileImplementation.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\DrawState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\DrawTileCommand.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\GeometryPool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexTerrainEngineNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexTerrainEngineDriver.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\LayerDrawable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\LoadTileData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\SelectionInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\SurfaceNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TerrainCuller.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TerrainRenderData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TileDrawable.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\EngineContext.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TileNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TileNodeRegistry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\Loader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\Unloader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\engine_rex\AutoGenShaders.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\Shaders.cpp.in">
      <Filter>Template Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\fdd003e6297f9db2dc920ad08683787e\AutoGenShaders.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\Common">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\CreateTileImplementation">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\DrawState">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\DrawTileCommand">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\GeometryPool">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\Shaders">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexTerrainEngineNode">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\LayerDrawable">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\LoadTileData">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RenderBindings">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\SurfaceNode">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TerrainCuller">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TerrainRenderData">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TileDrawable">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TileRenderModel">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\EngineContext">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TileNode">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\TileNodeRegistry">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\Loader">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\Unloader">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\SelectionInfo">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.elevation.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.gs.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.ImageLayer.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.NormalMap.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.Morphing.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.Tessellation.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.SDK.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.vert.GL4.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.ImageLayer.GL4.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.NormalMap.GL4.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.Tessellation.GL4.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.SDK.GL4.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\RexEngine.GL4.glsl">
      <Filter>Shader Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{90A39B65-D29D-3C94-AC0B-DC09770202F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{A1FE94ED-136E-3817-88DB-48371F9CD55A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shader Files">
      <UniqueIdentifier>{22749CC1-2900-3D69-B69E-CD0244498527}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{CAAE6D84-9DFC-3582-BE34-ED46F5C123C9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Template Files">
      <UniqueIdentifier>{D6D214E8-42C0-388A-8249-517009CA03A5}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
