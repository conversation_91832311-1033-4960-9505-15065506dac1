/* osgEarth Simple Map Viewer for Elevation Testing
 * Simplified version to test elevation configurations
 */

#include <osgViewer/Viewer>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/ElevationLayer>
#include <osgEarth/GLUtils>
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgDB/ReadFile>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/StateSetManipulator>
#include <osgEarth/Viewpoint>
#include <osgGA/GUIEventHandler>
#include <osgGA/GUIEventAdapter>
#include <osg/Notify>
#include <osgDB/FileUtils>
#include <osgDB/Registry>

using namespace osgEarth;
using namespace osgEarth::Util;

// Simple event handler for picking coordinates
class SimplePickHandler : public osgGA::GUIEventHandler
{
public:
  SimplePickHandler(MapNode *mapNode) : _mapNode(mapNode) {}

  virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
  {
    if (!_mapNode)
      return false;

    osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(&aa);
    if (!viewer)
      return false;

    switch (ea.getEventType())
    {
    case (osgGA::GUIEventAdapter::RELEASE):
    {
      if (ea.getButton() == osgGA::GUIEventAdapter::LEFT_MOUSE_BUTTON)
      {
        osgUtil::LineSegmentIntersector::Intersections intersections;
        if (viewer->computeIntersections(ea, intersections))
        {
          const osgUtil::LineSegmentIntersector::Intersection &hit = *intersections.begin();
          osg::Vec3d worldPos = hit.getWorldIntersectPoint();

          // Convert world coordinates to geographic
          const SpatialReference *mapSRS = _mapNode->getMap()->getSRS();
          const SpatialReference *geoSRS = mapSRS->getGeographicSRS();

          GeoPoint mapPoint;
          mapPoint.fromWorld(mapSRS, worldPos);
          GeoPoint geoPoint = mapPoint.transform(geoSRS);

          // Calculate elevation
          double elevationMSL = geoPoint.z();
          double distanceFromCenter = worldPos.length();
          double earthRadius = 6371000.0;
          double altitudeAboveEllipsoid = distanceFromCenter - earthRadius;

          OE_NOTICE << std::fixed << std::setprecision(6)
                    << "Pick Result -> "
                    << "Lat: " << geoPoint.y() << "°"
                    << ", Lon: " << geoPoint.x() << "°"
                    << ", Elev MSL: " << std::setprecision(1) << elevationMSL << "m"
                    << ", Alt above ellipsoid: " << altitudeAboveEllipsoid << "m"
                    << std::endl;
        }
      }
      break;
    }
    case (osgGA::GUIEventAdapter::KEYDOWN):
    {
      if (ea.getKey() == 'h' || ea.getKey() == 'H')
      {
        printHelp();
        return true;
      }
      else if (ea.getKey() == 'e' || ea.getKey() == 'E')
      {
        // Test elevation layers
        if (_mapNode.valid())
        {
          OE_NOTICE << "[Elevation Test] Checking elevation layers..." << std::endl;

          Map *map = _mapNode->getMap();
          LayerVector layers;
          map->getLayers(layers);

          unsigned int elevLayerCount = 0;
          for (LayerVector::const_iterator i = layers.begin(); i != layers.end(); ++i)
          {
            ElevationLayer *elevLayer = dynamic_cast<ElevationLayer *>(i->get());
            if (elevLayer)
            {
              elevLayerCount++;
              OE_NOTICE << "[Elevation Test] Layer #" << elevLayerCount << ": "
                        << elevLayer->getName()
                        << " (status: " << elevLayer->getStatus().toString() << ")"
                        << " (open: " << (elevLayer->isOpen() ? "YES" : "NO") << ")"
                        << " (visible: " << (elevLayer->getVisible() ? "YES" : "NO") << ")" << std::endl;
            }
          }

          if (elevLayerCount == 0)
          {
            OE_NOTICE << "[Elevation Test] WARNING: No elevation layers found!" << std::endl;
          }
          else
          {
            OE_NOTICE << "[Elevation Test] Found " << elevLayerCount << " elevation layer(s)" << std::endl;
          }
        }
        return true;
      }
      break;
    }
    default:
      break;
    }
    return false;
  }

  static void printHelp()
  {
    OE_NOTICE << "\n"
              << "osgearth_map_simple help:\n"
              << "  Mouse:\n"
              << "    Left click: pick geo coordinates\n"
              << "  Keyboard:\n"
              << "    h: Show this help message\n"
              << "    e: Test elevation layers\n"
              << "    ESC: Quit application\n"
              << std::endl;
  }

protected:
  osg::observer_ptr<MapNode> _mapNode;
};

int main(int argc, char **argv)
{
  // --- Force-load essential plugins first ---
  // This can help resolve initialization order issues, especially when
  // core dependencies like GDAL have been removed.
  osgDB::Registry::instance()->loadLibrary("osgdb_earth");
  osgDB::Registry::instance()->loadLibrary("osgdb_osgearth_engine_rex");
  // ------------------------------------------

  // Set verbose log level
  osgEarth::setNotifyLevel(osg::INFO);
  osgEarth::initialize();

  // Parse arguments
  osg::ArgumentParser arguments(&argc, argv);

  // --- Add plugin path programmatically ---
  std::string appPath = osgDB::getFilePath(arguments.getApplicationName());
  if (!appPath.empty())
  {
    std::string pluginPath = appPath + "/osgPlugins-3.6.5";
    osgDB::Registry::instance()->getLibraryFilePathList().push_back(pluginPath);
    OE_NOTICE << "[Setup] Added explicit plugin path: " << pluginPath << std::endl;
  }
  // ------------------------------------

  // Default to minimal elevation test
  std::string earthFile = "minimal_elevation.earth";
  if (arguments.argc() > 1 && !arguments.isOption(1))
  {
    earthFile = arguments[1];
  }

  OE_NOTICE << "Loading earth file: " << earthFile << std::endl;

  // Load the earth file
  osg::ref_ptr<osg::Node> node = osgDB::readNodeFile(earthFile);
  osg::ref_ptr<MapNode> mapNode = MapNode::findMapNode(node.get());

  if (!mapNode.valid())
  {
    OE_FATAL << "Failed to load earth file: " << earthFile << std::endl;
    return 1;
  }

  OE_NOTICE << "Earth file loaded successfully!" << std::endl;

  // Create viewer
  osgViewer::Viewer viewer(arguments);
  viewer.setRealizeOperation(new GL3RealizeOperation());
  viewer.addEventHandler(new osgViewer::StatsHandler);
  viewer.addEventHandler(new osgViewer::WindowSizeHandler);
  viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));

  // Earth manipulator
  osg::ref_ptr<osgEarth::Util::EarthManipulator> manip = new osgEarth::Util::EarthManipulator();
  viewer.setCameraManipulator(manip);

  // Set home viewpoint
  manip->setHomeViewpoint(
      Viewpoint("home", 0.0, 0.0, 0.0, 0.0, -90.0, 10000000.0), // Global view
      3.0);

  // Add our simple handler
  viewer.addEventHandler(new SimplePickHandler(mapNode.get()));

  // Set up window
  viewer.setUpViewInWindow(100, 100, 1280, 720);
  viewer.setSceneData(node.get());

  // Print help
  SimplePickHandler::printHelp();

  OE_NOTICE << "Starting viewer. Use left click to test elevation picking." << std::endl;

  // Run
  return viewer.run();
}