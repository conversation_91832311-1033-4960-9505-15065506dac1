/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#include <osgViewer/Viewer>

#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/ModelLayer>
#include <osgEarth/GeoTransform>
#include <osgEarth/GLUtils>
#include <osgEarth/ElevationLayer>
#include <osgEarth/Sky>

#include <osgEarth/TMS>
#include <osgEarth/WMS>
// #include <osgEarth/GDAL>
#include <osgEarth/XYZ>
#include <osgEarth/Composite>

#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/AutoScaleCallback>

#include <osgEarth/TerrainConstraintLayer>
// #include <osgEarth/OGRFeatureSource>

#include <osg/PositionAttitudeTransform>
#include <osgDB/ReadFile>
#include <osgDB/WriteFile>
#include <osgDB/Registry>
#include <osgDB/Options>
#include <osgUtil/Optimizer>
#include <osgUtil/Statistics>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/StateSetManipulator>
#include <osgEarth/URI>
#include <osgEarth/Config>
#include <osgEarth/Viewpoint>

#include <osgGA/GUIEventHandler>
#include <osgGA/GUIEventAdapter>
#include <osg/Notify>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/TileKey>

using namespace osgEarth;
using namespace osgEarth::Util;

// Forward declaration
static void printHelp();

// LOD Distance Callback Class - Monitor camera distance changes and trigger tile loading
class LODDistanceCallback : public osg::NodeCallback
{
public:
    LODDistanceCallback(MapNode *mapNode) : _mapNode(mapNode), _lastDistance(-1.0) {}

    virtual void operator()(osg::Node *node, osg::NodeVisitor *nv)
    {
        if (_mapNode.valid() && nv->getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
        {
            // Get camera from viewer if possible
            osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(nv);
            if (viewer)
            {
                calculateAndReportDistance(viewer->getCamera());
            }
        }

        // Continue traversal
        traverse(node, nv);
    }

private:
    void calculateAndReportDistance(osg::Camera *camera)
    {
        if (!camera || !_mapNode.valid())
            return;

        // Get camera world position
        osg::Vec3d cameraPos = camera->getInverseViewMatrix().getTrans();

        // Convert to geographic coordinates properly
        const SpatialReference *mapSRS = _mapNode->getMap()->getSRS();
        const SpatialReference *geoSRS = mapSRS->getGeographicSRS();

        GeoPoint cameraGeoPoint;
        cameraGeoPoint.fromWorld(mapSRS, cameraPos);

        // Transform to geographic coordinates if needed
        GeoPoint geoPoint = cameraGeoPoint.transform(geoSRS);

        // Calculate distance from camera to earth surface (altitude above ellipsoid)
        // For LOD calculation, we want the distance from camera to earth center
        double earthRadius = 6371000.0; // Earth radius in meters
        double cameraDistanceFromCenter = cameraPos.length();
        double altitudeAboveEarth = cameraDistanceFromCenter - earthRadius;

        // Use absolute altitude for LOD calculation (always positive)
        double currentDistance = fabs(altitudeAboveEarth);

        // Only output when distance changes significantly to avoid log spam
        if (fabs(currentDistance - _lastDistance) > 1000.0) // 1000m threshold
        {
            _lastDistance = currentDistance;

            // Calculate suggested LOD based on distance
            int suggestedLOD = calculateLODFromDistance(currentDistance);

            OE_NOTICE << "[LOD Manager] Camera altitude: " << std::fixed << std::setprecision(1)
                      << currentDistance << "m (above earth surface), Suggested LOD: " << suggestedLOD
                      << " | Geo coords: " << std::setprecision(6)
                      << geoPoint.y() << "°N, " << geoPoint.x() << "°E" << std::endl;
        }
    }

    int calculateLODFromDistance(double distance)
    {
        // Calculate appropriate LOD level based on distance
        // Closer distance = higher LOD level (larger numbers)
        if (distance < 1000)
            return 18; // Below 1km, highest detail
        else if (distance < 5000)
            return 16; // Below 5km
        else if (distance < 25000)
            return 14; // Below 25km
        else if (distance < 100000)
            return 12; // Below 100km
        else if (distance < 500000)
            return 10; // Below 500km
        else if (distance < 2000000)
            return 8; // Below 2000km
        else
            return 6; // Very far distance
    }

    osg::observer_ptr<MapNode> _mapNode;
    double _lastDistance;
};

// Cache Manager Class - Monitor and manage tile cache
class CacheManager : public osg::NodeCallback
{
public:
    CacheManager(MapNode *mapNode) : _mapNode(mapNode), _frameCount(0) {}

    virtual void operator()(osg::Node *node, osg::NodeVisitor *nv)
    {
        if (_mapNode.valid() && nv->getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
        {
            _frameCount++;

            // Report cache status every 300 frames (about 5 seconds)
            if (_frameCount % 300 == 0)
            {
                reportCacheStatus();
            }
        }

        traverse(node, nv);
    }

private:
    void reportCacheStatus()
    {
        if (!_mapNode.valid())
            return;

        // Get map cache
        Map *map = _mapNode->getMap();
        if (map && map->getCache())
        {
            OE_NOTICE << "[Cache Manager] Cache is active and operational" << std::endl;

            // Try to get cache statistics if available
            Cache *cache = map->getCache();
            if (cache)
            {
                OE_NOTICE << "[Cache Manager] Cache type: " << cache->className() << std::endl;
            }
        }
        else
        {
            OE_NOTICE << "[Cache Manager] Warning: No cache configured!" << std::endl;
        }
    }

    osg::observer_ptr<MapNode> _mapNode;
    int _frameCount;
};

// Custom event handler for picking and keyboard shortcuts
class PickHandler : public osgGA::GUIEventHandler
{
public:
    PickHandler(MapNode *mapNode) : _mapNode(mapNode) {}

    virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
    {
        if (!_mapNode)
            return false;

        osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(&aa);
        if (!viewer)
            return false;

        switch (ea.getEventType())
        {
        // Mouse click for picking coordinates
        case (osgGA::GUIEventAdapter::RELEASE):
        {
            if (ea.getButton() == osgGA::GUIEventAdapter::LEFT_MOUSE_BUTTON)
            {
                osgUtil::LineSegmentIntersector::Intersections intersections;
                if (viewer->computeIntersections(ea, intersections))
                {
                    // We are interested in the intersection with the terrain
                    const osgUtil::LineSegmentIntersector::Intersection &hit = *intersections.begin();

                    osg::Vec3d worldPos = hit.getWorldIntersectPoint();

                    // Convert world coordinates to geographic (lat/lon/elevation)
                    const SpatialReference *mapSRS = _mapNode->getMap()->getSRS();
                    const SpatialReference *geoSRS = mapSRS->getGeographicSRS();

                    GeoPoint mapPoint;
                    mapPoint.fromWorld(mapSRS, worldPos);

                    GeoPoint geoPoint = mapPoint.transform(geoSRS);

                    // Calculate proper elevation above sea level
                    double elevationMSL = geoPoint.z();

                    // Also calculate distance from earth center for reference
                    double distanceFromCenter = worldPos.length();
                    double earthRadius = 6371000.0; // Earth radius in meters
                    double altitudeAboveEllipsoid = distanceFromCenter - earthRadius;

                    OE_NOTICE << std::fixed << std::setprecision(6)
                              << "Pick Result -> "
                              << "Lat: " << geoPoint.y() << "°"
                              << ", Lon: " << geoPoint.x() << "°"
                              << ", Elev MSL: " << std::setprecision(1) << elevationMSL << "m"
                              << ", Alt above ellipsoid: " << altitudeAboveEllipsoid << "m"
                              << std::endl;

                    // Additional debug info
                    OE_NOTICE << "[Pick Debug] World pos: (" << std::setprecision(1)
                              << worldPos.x() << ", " << worldPos.y() << ", " << worldPos.z() << ")"
                              << ", Distance from center: " << distanceFromCenter << "m" << std::endl;
                }
                else
                {
                    OE_NOTICE << "[Pick Result] No intersection found - clicking on sky/void" << std::endl;
                }
            }
            break;
        }

        // Keyboard shortcuts
        case (osgGA::GUIEventAdapter::KEYDOWN):
        {
            if (ea.getKey() == 'h' || ea.getKey() == 'H')
            {
                printHelp();
                return true;
            }
            else if (ea.getKey() == 'r' || ea.getKey() == 'R')
            {
                osgEarth::Util::EarthManipulator *manip = dynamic_cast<osgEarth::Util::EarthManipulator *>(viewer->getCameraManipulator());
                if (manip)
                {
                    manip->home(0.0);
                    OE_NOTICE << "View reset to home position." << std::endl;
                }
                return true;
            }
            else if (ea.getKey() == 'l' || ea.getKey() == 'L')
            {
                // Force refresh terrain and tiles
                if (_mapNode.valid())
                {
                    OE_NOTICE << "[User Command] Terrain refresh requested (manual trigger)" << std::endl;
                    // Note: Actual terrain refresh requires more complex API calls
                }
                return true;
            }
            else if (ea.getKey() == 'c' || ea.getKey() == 'C')
            {
                // Check cache status
                if (_mapNode.valid())
                {
                    Map *map = _mapNode->getMap();
                    if (map && map->getCache())
                    {
                        OE_NOTICE << "[User Command] Cache status check requested" << std::endl;
                        Cache *cache = map->getCache();
                        if (cache)
                        {
                            OE_NOTICE << "[Cache Info] Type: " << cache->className() << std::endl;
                        }
                    }
                }
                return true;
            }
            else if (ea.getKey() == 'd' || ea.getKey() == 'D')
            {
                // Display current camera information
                osgEarth::Util::EarthManipulator *manip = dynamic_cast<osgEarth::Util::EarthManipulator *>(viewer->getCameraManipulator());
                if (manip && _mapNode.valid())
                {
                    OE_NOTICE << "[Debug Info] Current camera status requested" << std::endl;
                    // Note: Detailed viewpoint info requires more complex API calls
                }
                return true;
            }
            else if (ea.getKey() == 'e' || ea.getKey() == 'E')
            {
                // Test elevation query at current view center
                if (_mapNode.valid())
                {
                    OE_NOTICE << "[Elevation Test] ====================" << std::endl;

                    // Get current view center
                    osgEarth::Util::EarthManipulator *manip = dynamic_cast<osgEarth::Util::EarthManipulator *>(viewer->getCameraManipulator());
                    if (manip)
                    {
                        // Get camera position and look direction
                        osg::Vec3d eye, center, up;
                        viewer->getCamera()->getViewMatrixAsLookAt(eye, center, up);

                        // Convert eye position to geographic
                        const SpatialReference *mapSRS = _mapNode->getMap()->getSRS();
                        const SpatialReference *geoSRS = mapSRS->getGeographicSRS();

                        GeoPoint eyeGeoPoint;
                        eyeGeoPoint.fromWorld(mapSRS, eye);
                        GeoPoint geoEye = eyeGeoPoint.transform(geoSRS);

                        OE_NOTICE << "[Elevation Test] Camera at: " << std::fixed << std::setprecision(6)
                                  << geoEye.y() << "°N, " << geoEye.x() << "°E, "
                                  << std::setprecision(1) << geoEye.z() << "m" << std::endl;

                        // Test if elevation layers are loaded
                        Map *map = _mapNode->getMap();
                        unsigned int elevLayerCount = 0;
                        unsigned int imageLayerCount = 0;

                        LayerVector layers;
                        map->getLayers(layers);

                        OE_NOTICE << "[Elevation Test] Total layers found: " << layers.size() << std::endl;

                        for (LayerVector::const_iterator i = layers.begin(); i != layers.end(); ++i)
                        {
                            Layer *layer = i->get();

                            ElevationLayer *elevLayer = dynamic_cast<ElevationLayer *>(layer);
                            if (elevLayer)
                            {
                                elevLayerCount++;
                                OE_NOTICE << "[Elevation Test] Elevation layer #" << elevLayerCount << ": "
                                          << elevLayer->getName()
                                          << " (status: " << elevLayer->getStatus().toString() << ")"
                                          << " (open: " << (elevLayer->isOpen() ? "YES" : "NO") << ")"
                                          << " (visible: " << (elevLayer->getVisible() ? "YES" : "NO") << ")" << std::endl;

                                // 尝试查询高程
                                GeoPoint testPoint(geoSRS, geoEye.x(), geoEye.y(), 0.0);
                                double elevation = 0.0;

                                // 注意：直接查询高程可能需要不同的API
                                OE_NOTICE << "[Elevation Test] Layer '" << elevLayer->getName()
                                          << "' - direct elevation query not available in this API version" << std::endl;
                            }

                            ImageLayer *imgLayer = dynamic_cast<ImageLayer *>(layer);
                            if (imgLayer)
                            {
                                imageLayerCount++;
                                OE_NOTICE << "[Elevation Test] Image layer #" << imageLayerCount << ": "
                                          << imgLayer->getName()
                                          << " (status: " << imgLayer->getStatus().toString() << ")"
                                          << " (open: " << (imgLayer->isOpen() ? "YES" : "NO") << ")"
                                          << " (visible: " << (imgLayer->getVisible() ? "YES" : "NO") << ")" << std::endl;
                            }
                        }

                        if (elevLayerCount == 0)
                        {
                            OE_NOTICE << "[Elevation Test] WARNING: No elevation layers found!" << std::endl;
                        }
                        else
                        {
                            OE_NOTICE << "[Elevation Test] Found " << elevLayerCount << " elevation layer(s)" << std::endl;
                        }

                        if (imageLayerCount == 0)
                        {
                            OE_NOTICE << "[Elevation Test] WARNING: No image layers found!" << std::endl;
                        }
                        else
                        {
                            OE_NOTICE << "[Elevation Test] Found " << imageLayerCount << " image layer(s)" << std::endl;
                        }

                        OE_NOTICE << "[Elevation Test] Layer analysis completed" << std::endl;
                    }

                    OE_NOTICE << "[Elevation Test] ====================" << std::endl;
                }
                return true;
            }
            else if (ea.getKey() == 't' || ea.getKey() == 'T')
            {
                // 强制触发瓦片重新加载
                if (_mapNode.valid())
                {
                    OE_NOTICE << "[Tile Refresh] Manual tile refresh requested" << std::endl;

                    // 简单检查地形引擎是否存在
                    if (_mapNode->getTerrainEngine())
                    {
                        OE_NOTICE << "[Tile Refresh] Terrain engine is available" << std::endl;
                        OE_NOTICE << "[Tile Refresh] Note: Try zooming in/out to trigger tile loading" << std::endl;
                    }
                    else
                    {
                        OE_NOTICE << "[Tile Refresh] WARNING: No terrain engine found!" << std::endl;
                    }
                }
                return true;
            }
            break;
        }
        default:
            break;
        }
        return false; // event not handled
    }

private:
    osg::observer_ptr<MapNode> _mapNode;
};

// Helper function for printing help
static void printHelp()
{
    OE_NOTICE << "\n"
              << "osgearth_map help:\n"
              << "  Mouse:\n"
              << "    Left button:   drag to pan\n"
              << "    Right button:  drag to zoom\n"
              << "    Middle button: drag to rotate\n"
              << "    Scroll wheel:  zoom in/out\n"
              << "    Left click:    pick geo coordinates\n"
              << "  Keyboard:\n"
              << "    h: Show this help message\n"
              << "    r: Reset view to home position\n"
              << "    l: Force terrain/tile refresh (LOD update)\n"
              << "    c: Check cache status\n"
              << "    d: Display current viewpoint debug info\n"
              << "    e: Test elevation query at current view center\n"
              << "    t: Force tile refresh and terrain reload\n"
              << "    f: Toggle fullscreen\n"
              << "    s: Toggle statistics\n"
              << "    w: Toggle wireframe/solid mode\n"
              << "    q/ESC: Quit application\n"
              << "  LOD System:\n"
              << "    - LOD changes automatically based on camera distance\n"
              << "    - Watch console for '[LOD Manager]' messages\n"
              << "    - Use 'l' key to force manual terrain refresh\n"
              << "    - Use 'd' key to see current distance and suggested LOD\n"
              << std::endl;
}

int usage(int argc, char **argv)
{
    OE_NOTICE
        << "\n"
        << argv[0]
        << "\n    Loads an earth file and displays it."
        << "\n    Usage: osgearth_map [earth_file]"
        << std::endl;

    return 0;
}

// Demonstrates how to subclass ImageLayer to directly create textures
// for use in a layer.
class MyTextureLayer : public ImageLayer
{
public:
    std::string _path;
    osg::ref_ptr<osg::Texture2D> _tex;

    META_Layer(osgEarth, MyTextureLayer, Options, ImageLayer, mytexturelayer);

    void setPath(const char *path)
    {
        _path = path;
    }

    Status openImplementation()
    {
        osg::ref_ptr<osg::Image> image = osgDB::readRefImageFile(_path);
        if (image.valid())
            _tex = new osg::Texture2D(image.get());
        else
            return Status(Status::ConfigurationError, "no path");

        // Establish a geospatial profile for the layer:
        setProfile(Profile::create(Profile::GLOBAL_GEODETIC));

        // Tell the layer to call createTexture:
        setUseCreateTexture();

        // Restrict the data extents of this layer to LOD 0 (in this case)
        addDataExtent(DataExtent(getProfile()->getExtent(), 0, 0));

        return Status::OK();
    }

    TextureWindow
    createTexture(const TileKey &key, ProgressCallback *progress) const
    {
        // Set the texture matrix corresponding to the tile key:
        osg::Matrixf textureMatrix;
        key.getExtent().createScaleBias(getProfile()->getExtent(), textureMatrix);
        return TextureWindow(_tex.get(), textureMatrix);
    }
};

void checkErrors(const Layer *layer)
{
    if (layer->getStatus().isError())
    {
        OE_WARN << "Layer " << layer->getName() << " : " << layer->getStatus().message() << std::endl;
    }
}

/**
 * How to create a simple osgEarth map and display it.
 */
int main(int argc, char **argv)
{
    // Set a more verbose log level to see tile requests and other info.
    osgEarth::setNotifyLevel(osg::INFO);

    osgEarth::initialize();

    // use an ArgumentParser object to manage the program arguments.
    osg::ArgumentParser arguments(&argc, argv);

    // Look for an earth file on the command line, otherwise load a default.
    std::string earthFile = "main.earth";
    if (arguments.argc() > 1 && !arguments.isOption(1))
    {
        earthFile = arguments[1];
    }

    // Create a MapNode to render the map by reading the earth file.
    osg::ref_ptr<osg::Node> node = osgDB::readNodeFile(earthFile);
    osg::ref_ptr<MapNode> mapNode = MapNode::findMapNode(node.get());

    if (!mapNode.valid())
    {
        OE_FATAL << "Failed to load earth file: " << earthFile << std::endl;
        return 1;
    }

    // Add an event handler to the viewer, this will override the default quit handler
    osgViewer::Viewer viewer(arguments);
    viewer.setRealizeOperation(new GL3RealizeOperation());
    viewer.addEventHandler(new osgViewer::StatsHandler);
    viewer.addEventHandler(new osgViewer::WindowSizeHandler);
    viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));

    // create a manipulator and set a home viewpoint
    osg::ref_ptr<osgEarth::Util::EarthManipulator> manip = new osgEarth::Util::EarthManipulator();

    // Configure the manipulator's settings to be aware of the map,
    // which is essential for proper LOD calculation and navigation.
    manip->getSettings()->setArcViewpointTransitions(true);
    manip->getSettings()->setLockAzimuthWhilePanning(false);

    viewer.setCameraManipulator(manip);

    // set a home viewpoint if the earth file doesn't contain one
    bool has_viewpoints = false;
    if (mapNode->getConfig().hasChild("viewpoints"))
    {
        has_viewpoints = !mapNode->getConfig().child("viewpoints").children("viewpoint").empty();
    }

    if (!has_viewpoints)
    {
        manip->setHomeViewpoint(
            Viewpoint("home", 104.0, 36.0, 0.0, 0.0, -90.0, 2.0e7), // Look at China
            5.0);                                                   // 5 seconds to fly home
    }

    // Add our custom handler
    viewer.addEventHandler(new PickHandler(mapNode.get()));

    // 添加LOD距离回调 - 监控相机距离变化并触发瓦片加载
    mapNode->addUpdateCallback(new LODDistanceCallback(mapNode.get()));
    OE_NOTICE << "[Setup] LOD Distance Callback installed" << std::endl;

    // 添加缓存管理器
    mapNode->addUpdateCallback(new CacheManager(mapNode.get()));
    OE_NOTICE << "[Setup] Cache Manager installed" << std::endl;

    // Force the application to start in a window for consistency
    viewer.setUpViewInWindow(100, 100, 1280, 720);

    // set the scene to render
    viewer.setSceneData(node.get());

    // 优化数据库分页器设置以改善瓦片加载
    if (viewer.getDatabasePager())
    {
        osgDB::DatabasePager *pager = viewer.getDatabasePager();

        // 设置分页器参数以优化瓦片加载
        pager->setUnrefImageDataAfterApplyPolicy(true, false);

        // 增加分页器的线程数量以加快瓦片加载
        pager->setUpThreads(4, 1); // 4个工作线程，1个HTTP线程

        // 设置目标帧率，确保分页不会阻塞渲染
        pager->setTargetMaximumNumberOfPageLOD(300);
        pager->setDeleteRemovedSubgraphsInDatabaseThread(true);

        OE_NOTICE << "[Setup] Database Pager optimized for tile loading" << std::endl;
        OE_NOTICE << "[Setup] - Worker threads: 4" << std::endl;
        OE_NOTICE << "[Setup] - HTTP threads: 1" << std::endl;
        OE_NOTICE << "[Setup] - Max PageLOD nodes: 300" << std::endl;
    }

    // 启用多线程渲染以提高性能
    viewer.setThreadingModel(osgViewer::Viewer::SingleThreaded); // 或 CullDrawThreadPerContext

    // Set camera near/far clipping planes, optimize rendering for large scale scenes
    double fovy, aspectRatio, zNear, zFar;
    viewer.getCamera()->getProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
    viewer.getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, 1.0, 1e10);

    OE_NOTICE << "[Setup] Camera clipping planes optimized for global view" << std::endl;

    // Print the help text as we start
    printHelp();

    // run the viewer
    return viewer.run();
}

// example of custom layer, not used by default.
#if 0
void addMyCustomLayer(MapNode* mapNode)
{
    // Programmatically add a new layer.
    // First, create a TMS image layer
    ImageLayer* tms = new ImageLayer(
        "My TMS Layer",
        TMSOptions("url", "http://readymap.org/readymap/tiles/1.0.0/7/"));

    // Add it to the map
    mapNode->getMap()->addLayer(tms);

    // zoom to the new layer
    Viewpoint vp("", -109.55, 39.05, -3500, -45.0, 45.0, 1500000.0);
    mapNode->getMapManipulator()->setViewpoint(vp, 5.0);
}
#endif