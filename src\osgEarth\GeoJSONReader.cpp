/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include "GeoJSONReader.h"
#include <osgEarth/Notify>
#include <osgEarth/StringUtils>
#include <fstream>
#include <sstream>

#define LC "[GeoJSONReader] "

using namespace osgEarth;

//........................................................................

GeoJSONReader::GeoJSONReader()
{
}

GeoJSONReader::~GeoJSONReader()
{
}

bool
GeoJSONReader::parseFile(const std::string& filename, std::vector<osg::ref_ptr<Feature>>& features)
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        setError("Cannot open file: " + filename);
        return false;
    }

    std::stringstream buffer;
    buffer << file.rdbuf();
    file.close();

    return parseString(buffer.str(), features);
}

bool
GeoJSONReader::parseString(const std::string& jsonString, std::vector<osg::ref_ptr<Feature>>& features)
{
    Json::Value root;
    Json::Reader reader;

    if (!reader.parse(jsonString, root))
    {
        setError("Failed to parse JSON: " + reader.getFormattedErrorMessages());
        return false;
    }

    if (!root.isObject())
    {
        setError("Root element is not a JSON object");
        return false;
    }

    std::string type = root.get("type", "").asString();
    
    if (type == "FeatureCollection")
    {
        return parseFeatureCollection(root, features);
    }
    else if (type == "Feature")
    {
        osg::ref_ptr<Feature> feature;
        if (parseFeature(root, feature) && feature.valid())
        {
            features.push_back(feature);
            return true;
        }
        return false;
    }
    else if (type == "Point" || type == "LineString" || type == "Polygon" ||
             type == "MultiPoint" || type == "MultiLineString" || type == "MultiPolygon" ||
             type == "GeometryCollection")
    {
        // Single geometry - wrap in a feature
        Geometry* geometry = parseGeometry(root);
        if (geometry)
        {
            osg::ref_ptr<Feature> feature = new Feature(geometry, SpatialReference::create("wgs84"));
            features.push_back(feature);
            return true;
        }
        return false;
    }
    else
    {
        setError("Unknown GeoJSON type: " + type);
        return false;
    }
}

bool
GeoJSONReader::parseFeatureCollection(const Json::Value& root, std::vector<osg::ref_ptr<Feature>>& features)
{
    if (!root.isMember("features") || !root["features"].isArray())
    {
        setError("FeatureCollection missing 'features' array");
        return false;
    }

    const Json::Value& featuresArray = root["features"];
    
    for (Json::ArrayIndex i = 0; i < featuresArray.size(); ++i)
    {
        osg::ref_ptr<Feature> feature;
        if (parseFeature(featuresArray[i], feature) && feature.valid())
        {
            features.push_back(feature);
        }
    }

    return true;
}

bool
GeoJSONReader::parseFeature(const Json::Value& featureJson, osg::ref_ptr<Feature>& feature)
{
    if (!featureJson.isObject())
    {
        setError("Feature is not a JSON object");
        return false;
    }

    if (featureJson.get("type", "").asString() != "Feature")
    {
        setError("Object is not a Feature");
        return false;
    }

    // Parse geometry
    Geometry* geometry = nullptr;
    if (featureJson.isMember("geometry") && !featureJson["geometry"].isNull())
    {
        geometry = parseGeometry(featureJson["geometry"]);
    }

    // Create feature
    feature = new Feature(geometry, SpatialReference::create("wgs84"));

    // Parse properties
    if (featureJson.isMember("properties") && featureJson["properties"].isObject())
    {
        parseProperties(featureJson["properties"], feature.get());
    }

    // Parse ID if present
    if (featureJson.isMember("id"))
    {
        const Json::Value& idValue = featureJson["id"];
        if (idValue.isString())
        {
            feature->setFID(std::hash<std::string>{}(idValue.asString()));
        }
        else if (idValue.isNumeric())
        {
            feature->setFID(static_cast<FeatureID>(idValue.asInt64()));
        }
    }

    return true;
}

Geometry*
GeoJSONReader::parseGeometry(const Json::Value& geometryJson)
{
    if (!geometryJson.isObject())
    {
        setError("Geometry is not a JSON object");
        return nullptr;
    }

    std::string type = geometryJson.get("type", "").asString();
    
    if (!geometryJson.isMember("coordinates") && type != "GeometryCollection")
    {
        setError("Geometry missing 'coordinates' member");
        return nullptr;
    }

    const Json::Value& coordinates = geometryJson["coordinates"];

    if (type == "Point")
    {
        return parsePoint(coordinates);
    }
    else if (type == "LineString")
    {
        return parseLineString(coordinates);
    }
    else if (type == "Polygon")
    {
        return parsePolygon(coordinates);
    }
    else if (type == "MultiPoint")
    {
        return parseMultiPoint(coordinates);
    }
    else if (type == "MultiLineString")
    {
        return parseMultiLineString(coordinates);
    }
    else if (type == "MultiPolygon")
    {
        return parseMultiPolygon(coordinates);
    }
    else if (type == "GeometryCollection")
    {
        return parseGeometryCollection(geometryJson["geometries"]);
    }
    else
    {
        setError("Unknown geometry type: " + type);
        return nullptr;
    }
}

Geometry*
GeoJSONReader::parsePoint(const Json::Value& coordinates)
{
    if (!coordinates.isArray() || coordinates.size() < 2)
    {
        setError("Point coordinates must be an array with at least 2 elements");
        return nullptr;
    }

    osg::Vec3d coord = parseCoordinate(coordinates);
    return new Point(coord);
}

Geometry*
GeoJSONReader::parseLineString(const Json::Value& coordinates)
{
    if (!coordinates.isArray())
    {
        setError("LineString coordinates must be an array");
        return nullptr;
    }

    LineString* lineString = new LineString();
    
    for (Json::ArrayIndex i = 0; i < coordinates.size(); ++i)
    {
        osg::Vec3d coord = parseCoordinate(coordinates[i]);
        lineString->push_back(coord);
    }

    return lineString;
}

Geometry*
GeoJSONReader::parsePolygon(const Json::Value& coordinates)
{
    if (!coordinates.isArray() || coordinates.size() == 0)
    {
        setError("Polygon coordinates must be a non-empty array");
        return nullptr;
    }

    Polygon* polygon = new Polygon();
    
    // First ring is exterior
    Ring* exteriorRing = parseRing(coordinates[0]);
    if (!exteriorRing)
        return nullptr;
    
    polygon->setExteriorRing(exteriorRing);
    
    // Additional rings are holes
    for (Json::ArrayIndex i = 1; i < coordinates.size(); ++i)
    {
        Ring* hole = parseRing(coordinates[i]);
        if (hole)
        {
            polygon->addHole(hole);
        }
    }

    return polygon;
}

Geometry*
GeoJSONReader::parseMultiPoint(const Json::Value& coordinates)
{
    if (!coordinates.isArray())
    {
        setError("MultiPoint coordinates must be an array");
        return nullptr;
    }

    MultiGeometry* multiGeom = new MultiGeometry();
    
    for (Json::ArrayIndex i = 0; i < coordinates.size(); ++i)
    {
        Geometry* point = parsePoint(coordinates[i]);
        if (point)
        {
            multiGeom->add(point);
        }
    }

    return multiGeom;
}

Geometry*
GeoJSONReader::parseMultiLineString(const Json::Value& coordinates)
{
    if (!coordinates.isArray())
    {
        setError("MultiLineString coordinates must be an array");
        return nullptr;
    }

    MultiGeometry* multiGeom = new MultiGeometry();
    
    for (Json::ArrayIndex i = 0; i < coordinates.size(); ++i)
    {
        Geometry* lineString = parseLineString(coordinates[i]);
        if (lineString)
        {
            multiGeom->add(lineString);
        }
    }

    return multiGeom;
}

Geometry*
GeoJSONReader::parseMultiPolygon(const Json::Value& coordinates)
{
    if (!coordinates.isArray())
    {
        setError("MultiPolygon coordinates must be an array");
        return nullptr;
    }

    MultiGeometry* multiGeom = new MultiGeometry();
    
    for (Json::ArrayIndex i = 0; i < coordinates.size(); ++i)
    {
        Geometry* polygon = parsePolygon(coordinates[i]);
        if (polygon)
        {
            multiGeom->add(polygon);
        }
    }

    return multiGeom;
}

Geometry*
GeoJSONReader::parseGeometryCollection(const Json::Value& geometries)
{
    if (!geometries.isArray())
    {
        setError("GeometryCollection geometries must be an array");
        return nullptr;
    }

    MultiGeometry* multiGeom = new MultiGeometry();
    
    for (Json::ArrayIndex i = 0; i < geometries.size(); ++i)
    {
        Geometry* geometry = parseGeometry(geometries[i]);
        if (geometry)
        {
            multiGeom->add(geometry);
        }
    }

    return multiGeom;
}

osg::Vec3d
GeoJSONReader::parseCoordinate(const Json::Value& coord)
{
    if (!coord.isArray() || coord.size() < 2)
    {
        return osg::Vec3d(0, 0, 0);
    }

    double x = coord[0].asDouble();
    double y = coord[1].asDouble();
    double z = (coord.size() > 2) ? coord[2].asDouble() : 0.0;

    return osg::Vec3d(x, y, z);
}

Ring*
GeoJSONReader::parseRing(const Json::Value& ringCoords)
{
    if (!ringCoords.isArray())
    {
        setError("Ring coordinates must be an array");
        return nullptr;
    }

    Ring* ring = new Ring();
    
    for (Json::ArrayIndex i = 0; i < ringCoords.size(); ++i)
    {
        osg::Vec3d coord = parseCoordinate(ringCoords[i]);
        ring->push_back(coord);
    }

    return ring;
}

void
GeoJSONReader::parseProperties(const Json::Value& properties, Feature* feature)
{
    if (!properties.isObject())
        return;

    Json::Value::Members members = properties.getMemberNames();
    
    for (const std::string& member : members)
    {
        const Json::Value& value = properties[member];
        
        if (value.isString())
        {
            feature->set(member, value.asString());
        }
        else if (value.isInt())
        {
            feature->set(member, value.asInt());
        }
        else if (value.isDouble())
        {
            feature->set(member, value.asDouble());
        }
        else if (value.isBool())
        {
            feature->set(member, value.asBool());
        }
        // For other types, convert to string
        else if (!value.isNull())
        {
            feature->set(member, value.asString());
        }
    }
}

void
GeoJSONReader::setError(const std::string& error)
{
    _lastError = error;
    OE_WARN << LC << error << std::endl;
}
