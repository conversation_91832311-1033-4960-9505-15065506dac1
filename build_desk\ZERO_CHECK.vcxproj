﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4A2C7910-819B-3DF4-9EF6-60A9CC2F4215}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\2ecc4ca1658b105f0b081e581582c3c6\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompileFeatures.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompileFeatures.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompileFeatures.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/OSGEARTH.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompileFeatures.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenGL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSceneGraph.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgDB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgGA.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgManipulator.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgShadow.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgSim.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgText.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgUtil.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindosgViewer.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Findosg_functions.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\WebP\WebPTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\cmake_uninstall.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\install-package-config-files.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_ios.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_osx.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_unix.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\oe_win32.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-config.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\osgearth-macros.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\docs\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\applications\osgearth_map\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\bumpmap\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_filesystem\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\cache_rocksdb\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\colorramp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\detail\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\earth\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\engine_rex\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\fastdxt\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_intersect\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\featurefilter_join\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\gltf\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\kml\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\lerc\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\script_engine_duktape\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_gl\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\template\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\terrainshader\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm2008\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm84\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\vdatum_egm96\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\viewpoints\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\webp\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\zip\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthImGui\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\bumpmap\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_filesystem\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\colorramp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\detail\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\earth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\engine_rex\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_intersect\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\featurefilter_join\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\kml\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\script_engine_duktape\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_gl\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\template\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\terrainshader\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm2008\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm84\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\vdatum_egm96\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\viewpoints\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\webp\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\zip\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\gltf\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\cache_rocksdb\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\fastdxt\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\lerc\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\applications\osgearth_map\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\docs\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>