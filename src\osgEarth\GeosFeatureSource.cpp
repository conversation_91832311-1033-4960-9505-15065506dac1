/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#include <osgEarth/GeosFeatureSource>
#include <osgEarth/FeatureCursor>
#include <osgEarth/Filter>
#include <osgEarth/JsonUtils>
#include <osgEarth/StringUtils>
#include <osgEarth/FileUtils>
#include <osgEarth/Metrics>
#include <osgDB/FileUtils>
#include "ShapefileReader.h"
#include "GeoJSONReader.h"
#include <fstream>
#include <sstream>

#define LC "[GeosFeatureSource] " << getName() << " : "

using namespace osgEarth;

#define OE_DEVEL OE_NULL

//........................................................................

Config
GeosFeatureSource::Options::getConfig() const
{
    Config conf = super::Options::getConfig();
    conf.set("url", _url);
    conf.set("connection", _connection);
    conf.set("format", _format);
    conf.set("build_spatial_index", _buildSpatialIndex);
    conf.set("force_rebuild_spatial_index", _forceRebuildSpatialIndex);
    conf.set("geometry", _geometryConfig);
    conf.set("geometry_url", _geometryUrl);
    conf.set("layer", _layer);
    conf.set("query", _query);
    return conf;
}

void GeosFeatureSource::Options::fromConfig(const Config &conf)
{
    conf.get("url", _url);
    conf.get("connection", _connection);
    conf.get("format", _format);
    conf.get("build_spatial_index", _buildSpatialIndex);
    conf.get("force_rebuild_spatial_index", _forceRebuildSpatialIndex);
    conf.get("geometry", _geometryConfig);
    conf.get("geometry_url", _geometryUrl);
    conf.get("layer", _layer);
    conf.get("query", _query);
}

//........................................................................

REGISTER_OSGEARTH_LAYER(geosfeatures, GeosFeatureSource);

GeosFeatureSource::~GeosFeatureSource()
{
    // nop
}

void GeosFeatureSource::setURL(const URI &value)
{
    options().url() = value;
}

const URI &
GeosFeatureSource::getURL() const
{
    return options().url().value();
}

void GeosFeatureSource::setConnection(const std::string &value)
{
    options().connection() = value;
}

const std::string &
GeosFeatureSource::getConnection() const
{
    return options().connection().value();
}

void GeosFeatureSource::setFormat(const std::string &value)
{
    options().format() = value;
}

const std::string &
GeosFeatureSource::getFormat() const
{
    return options().format().value();
}

void GeosFeatureSource::setBuildSpatialIndex(const bool &value)
{
    options().buildSpatialIndex() = value;
}

const bool &
GeosFeatureSource::getBuildSpatialIndex() const
{
    return options().buildSpatialIndex().value();
}

void GeosFeatureSource::setGeometryConfig(const Config &value)
{
    options().geometryConfig() = value;
}

const Config &
GeosFeatureSource::getGeometryConfig() const
{
    return options().geometryConfig().value();
}

void GeosFeatureSource::setGeometryUrl(const URI &value)
{
    options().geometryUrl() = value;
}

const URI &
GeosFeatureSource::getGeometryUrl() const
{
    return options().geometryUrl().value();
}

void GeosFeatureSource::setLayer(const std::string &value)
{
    options().layer() = value;
}

const std::string &
GeosFeatureSource::getLayer() const
{
    return options().layer().value();
}

void GeosFeatureSource::setQuery(const Query &value)
{
    options().query() = value;
}

const Query &
GeosFeatureSource::getQuery() const
{
    return options().query().value();
}

void GeosFeatureSource::setGeometry(Geometry *geom)
{
    _geometry = geom;
}

const Geometry *
GeosFeatureSource::getGeometry() const
{
    return _geometry.get();
}

Status
GeosFeatureSource::openImplementation()
{
    Status parent = FeatureSource::openImplementation();
    if (parent.isError())
        return parent;

#ifdef OSGEARTH_HAVE_GEOS
    // Initialize GEOS context
    _geosContext = GEOS_init_r();
    if (!_geosContext)
    {
        return Status(Status::ResourceUnavailable, "Failed to initialize GEOS context");
    }
#else
    return Status(Status::ServiceUnavailable, "GEOS library not available");
#endif

    // Load data based on format
    std::string format = getFormat();
    if (format.empty())
    {
        // Auto-detect format from file extension
        std::string url = getURL().full();
        if (osgEarth::Util::endsWith(url, ".shp", false))
            format = "shapefile";
        else if (osgEarth::Util::endsWith(url, ".json", false) || osgEarth::Util::endsWith(url, ".geojson", false))
            format = "geojson";
    }

    bool success = false;
    if (format == "shapefile")
    {
        success = loadShapefile(getURL().full());
    }
    else if (format == "geojson")
    {
        success = loadGeoJSON(getURL().full());
    }
    else if (!getGeometry())
    {
        return Status(Status::ConfigurationError, "Unsupported format or no explicit geometry provided");
    }

    if (!success && !getGeometry())
    {
        return Status(Status::ResourceUnavailable, "Failed to load feature data");
    }

    // Initialize schema and profile
    initSchema();

    return Status::NoError;
}

Status
GeosFeatureSource::closeImplementation()
{
#ifdef OSGEARTH_HAVE_GEOS
    if (_geosContext)
    {
        GEOS_finish_r(static_cast<GEOSContextHandle_t>(_geosContext));
        _geosContext = nullptr;
    }
#endif

    _features.clear();
    return FeatureSource::closeImplementation();
}

bool GeosFeatureSource::loadShapefile(const std::string &filename)
{
    ShapefileReader reader;
    if (!reader.open(filename))
    {
        OE_WARN << LC << "Failed to open shapefile: " << filename << std::endl;
        return false;
    }

    std::vector<osg::ref_ptr<Feature>> features;
    if (!reader.readFeatures(features))
    {
        OE_WARN << LC << "Failed to read features from shapefile: " << filename << std::endl;
        reader.close();
        return false;
    }

    _features = std::move(features);
    reader.close();

    OE_INFO << LC << "Loaded " << _features.size() << " features from shapefile" << std::endl;
    return true;
}

bool GeosFeatureSource::loadGeoJSON(const std::string &filename)
{
    GeoJSONReader reader;
    std::vector<osg::ref_ptr<Feature>> features;

    if (!reader.parseFile(filename, features))
    {
        OE_WARN << LC << "Failed to parse GeoJSON file: " << filename
                << " - " << reader.getLastError() << std::endl;
        return false;
    }

    _features = std::move(features);
    OE_INFO << LC << "Loaded " << _features.size() << " features from GeoJSON" << std::endl;
    return true;
}

bool GeosFeatureSource::parseGeoJSONString(const std::string &jsonData)
{
    GeoJSONReader reader;
    std::vector<osg::ref_ptr<Feature>> features;

    if (!reader.parseString(jsonData, features))
    {
        OE_WARN << LC << "Failed to parse GeoJSON string: " << reader.getLastError() << std::endl;
        return false;
    }

    _features = std::move(features);
    OE_INFO << LC << "Loaded " << _features.size() << " features from GeoJSON string" << std::endl;
    return true;
}

void GeosFeatureSource::initSchema()
{
    // Initialize basic schema
    _schema = FeatureSchema();
    _featureCount = _features.size();
}

FeatureCursor *
GeosFeatureSource::createFeatureCursorImplementation(const Query &query, ProgressCallback *progress) const
{
    return new Geos::GeosFeatureCursor(
        _features,
        this,
        getFeatureProfile(),
        query,
        getFilters(),
        options().rewindPolygons().value(),
        progress);
}

bool GeosFeatureSource::deleteFeature(FeatureID fid)
{
    // TODO: Implement feature deletion
    return false;
}

int GeosFeatureSource::getFeatureCount() const
{
    return _featureCount;
}

bool GeosFeatureSource::supportsGetFeature() const
{
    return true;
}

Feature *
GeosFeatureSource::getFeature(FeatureID fid)
{
    for (auto &feature : _features)
    {
        if (feature->getFID() == fid)
        {
            return feature.get();
        }
    }
    return nullptr;
}

bool GeosFeatureSource::isWritable() const
{
    return _writable;
}

const FeatureSchema &
GeosFeatureSource::getSchema() const
{
    return _schema;
}

bool GeosFeatureSource::insertFeature(Feature *feature)
{
    // TODO: Implement feature insertion
    return false;
}

osgEarth::Geometry::Type
GeosFeatureSource::getGeometryType() const
{
    return _geometryType;
}

const Status &
GeosFeatureSource::create(
    const FeatureProfile *profile,
    const FeatureSchema &schema,
    const Geometry::Type &geometryType,
    const osgDB::Options *readOptions)
{
    // TODO: Implement feature source creation
    static Status s = Status(Status::ServiceUnavailable, "Create not implemented");
    return s;
}

void GeosFeatureSource::buildSpatialIndex()
{
    // TODO: Implement spatial index building
}

void GeosFeatureSource::dirty()
{
    FeatureSource::dirty();
}

Geometry *
GeosFeatureSource::parseGeometry(const Config &geomConf)
{
    // TODO: Implement WKT geometry parsing
    return nullptr;
}

Geometry *
GeosFeatureSource::parseGeometryUrl(const URI &geomUrl, const osgDB::Options *dbOptions)
{
    // TODO: Implement geometry URL parsing
    return nullptr;
}

//........................................................................
// GeosFeatureCursor implementation

Geos::GeosFeatureCursor::GeosFeatureCursor(
    const std::vector<osg::ref_ptr<Feature>> &features,
    const FeatureSource *source,
    const FeatureProfile *profile,
    const Query &query,
    const FeatureFilterChain &filters,
    bool rewindPolygons,
    ProgressCallback *progress) : _current(features.begin()),
                                  _end(features.end()),
                                  _query(query),
                                  _source(source),
                                  _profile(profile),
                                  _filters(filters),
                                  _rewindPolygons(rewindPolygons)
{
    readChunk();
}

Geos::GeosFeatureCursor::~GeosFeatureCursor()
{
    // nop
}

bool Geos::GeosFeatureCursor::hasMore() const
{
    return !_queue.empty() || _current != _end;
}

Feature *
Geos::GeosFeatureCursor::nextFeature()
{
    if (_queue.empty())
        readChunk();

    if (!_queue.empty())
    {
        _lastFeatureReturned = _queue.front();
        _queue.pop();
        return _lastFeatureReturned.release();
    }
    return nullptr;
}

void Geos::GeosFeatureCursor::readChunk()
{
    while (_current != _end && _queue.size() < 500) // chunk size
    {
        Feature *feature = _current->get();
        if (matchesQuery(feature))
        {
            // Apply filters
            FilterContext context;
            context.profile() = _profile;

            bool passed = true;
            // TODO: Implement proper filter processing
            // For now, skip filter processing

            if (passed)
            {
                _queue.push(osg::ref_ptr<Feature>(feature));
            }
        }
        ++_current;
    }
}

bool Geos::GeosFeatureCursor::matchesQuery(const Feature *feature) const
{
    // TODO: Implement query matching logic
    // For now, return true to include all features
    return true;
}
