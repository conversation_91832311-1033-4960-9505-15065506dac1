EXPORTS 
	??_R0?AUDrawCallback@Camera@osg@@@8 	 DATA
	??_R0?AVCallback@osg@@@8 	 DATA
	??_R0?AVObject@osg@@@8 	 DATA
	??_R0?AVReferenced@osg@@@8 	 DATA
	??_R0?AVbad_alloc@std@@@8 	 DATA
	??_R0?AVbad_array_new_length@std@@@8 	 DATA
	??_R0?AVexception@std@@@8 	 DATA
	?EmptyString@ImGuiTextBuffer@@2PADA 	 DATA
	?GImGui@@3PEAUImGuiContext@@EA 	 DATA
	?GImGuiDemoMarkerCallback@@3P6AXPEBDH0PEAX@ZEA 	 DATA
	?GImGuiDemoMarkerCallbackUserData@@3PEAXEA 	 DATA
	?GImNodes@@3PEAUImNodesContext@@EA 	 DATA
	?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA 	 DATA
	?_OptionsStorage@?1??__local_stdio_scanf_options@@9@4_KA 	 DATA
	_Avx2WmemEnabledWeakValue 	 DATA
	imgl3wProcs 	 DATA
	??$?0$$V@?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@U_Zero_then_variadic_args_t@1@@Z
	??$?0D@?$allocator@U_Container_proxy@std@@@std@@QEAA@AEBV?$allocator@D@1@@Z
	??$?0V?$allocator@D@std@@$$V@?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@U_One_then_variadic_args_t@1@$$QEAV?$allocator@D@1@@Z
	??$?0V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$allocator@U_Container_proxy@std@@@std@@QEAA@AEBV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@@Z
	??$?6U?$char_traits@D@std@@@std@@YAAEAV?$basic_ostream@DU?$char_traits@D@std@@@0@AEAV10@PEBD@Z
	??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@$$QEAV10@AEBV10@@Z
	??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@$$QEAV10@D@Z
	??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@$$QEAV10@QEBD@Z
	??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@AEBV10@QEBD@Z
	??$?HDU?$char_traits@D@std@@V?$allocator@D@1@@std@@YA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@QEBDAEBV10@@Z
	??$CheckboxFlagsT@H@ImGui@@YA_NPEBDPEAHH@Z
	??$CheckboxFlagsT@I@ImGui@@YA_NPEBDPEAII@Z
	??$CheckboxFlagsT@_J@ImGui@@YA_NPEBDPEA_J_J@Z
	??$CheckboxFlagsT@_K@ImGui@@YA_NPEBDPEA_K_K@Z
	??$DragBehaviorT@HHM@ImGui@@YA_NHPEAHMHHPEBDH@Z
	??$DragBehaviorT@IHM@ImGui@@YA_NHPEAIMIIPEBDH@Z
	??$DragBehaviorT@MMM@ImGui@@YA_NHPEAMMMMPEBDH@Z
	??$DragBehaviorT@NNN@ImGui@@YA_NHPEANMNNPEBDH@Z
	??$DragBehaviorT@_J_JN@ImGui@@YA_NHPEA_JM_J1PEBDH@Z
	??$DragBehaviorT@_K_JN@ImGui@@YA_NHPEA_KM_K1PEBDH@Z
	??$GetSpan@F@?$ImSpanAllocator@$05@@QEAAXHPEAU?$ImSpan@F@@@Z
	??$GetSpan@UImGuiTableCellData@@@?$ImSpanAllocator@$05@@QEAAXHPEAU?$ImSpan@UImGuiTableCellData@@@@@Z
	??$GetSpan@UImGuiTableColumn@@@?$ImSpanAllocator@$05@@QEAAXHPEAU?$ImSpan@UImGuiTableColumn@@@@@Z
	??$IM_DELETE@D@@YAXPEAD@Z
	??$IM_DELETE@UImDrawList@@@@YAXPEAUImDrawList@@@Z
	??$IM_DELETE@UImFont@@@@YAXPEAUImFont@@@Z
	??$IM_DELETE@UImFontAtlas@@@@YAXPEAUImFontAtlas@@@Z
	??$IM_DELETE@UImGuiContext@@@@YAXPEAUImGuiContext@@@Z
	??$IM_DELETE@UImGuiDockNode@@@@YAXPEAUImGuiDockNode@@@Z
	??$IM_DELETE@UImGuiTabBar@@@@YAXPEAUImGuiTabBar@@@Z
	??$IM_DELETE@UImGuiViewportP@@@@YAXPEAUImGuiViewportP@@@Z
	??$IM_DELETE@UImGuiWindow@@@@YAXPEAUImGuiWindow@@@Z
	??$IM_DELETE@UImGui_ImplOpenGL3_Data@@@@YAXPEAUImGui_ImplOpenGL3_Data@@@Z
	??$IM_DELETE@UImNodesContext@@@@YAXPEAUImNodesContext@@@Z
	??$RoundScalarWithFormatT@H@ImGui@@YAHPEBDHH@Z
	??$RoundScalarWithFormatT@I@ImGui@@YAIPEBDHI@Z
	??$RoundScalarWithFormatT@M@ImGui@@YAMPEBDHM@Z
	??$RoundScalarWithFormatT@N@ImGui@@YANPEBDHN@Z
	??$RoundScalarWithFormatT@_J@ImGui@@YA_JPEBDH_J@Z
	??$RoundScalarWithFormatT@_K@ImGui@@YA_KPEBDH_K@Z
	??$ScaleRatioFromValueT@HHM@ImGui@@YAMHHHH_NMM@Z
	??$ScaleRatioFromValueT@IHM@ImGui@@YAMHIII_NMM@Z
	??$ScaleRatioFromValueT@MMM@ImGui@@YAMHMMM_NMM@Z
	??$ScaleRatioFromValueT@NNN@ImGui@@YAMHNNN_NMM@Z
	??$ScaleRatioFromValueT@_J_JN@ImGui@@YAMH_J00_NMM@Z
	??$ScaleRatioFromValueT@_K_JN@ImGui@@YAMH_K00_NMM@Z
	??$ScaleValueFromRatioT@HHM@ImGui@@YAHHMHH_NMM@Z
	??$ScaleValueFromRatioT@IHM@ImGui@@YAIHMII_NMM@Z
	??$ScaleValueFromRatioT@MMM@ImGui@@YAMHMMM_NMM@Z
	??$ScaleValueFromRatioT@NNN@ImGui@@YANHMNN_NMM@Z
	??$ScaleValueFromRatioT@_J_JN@ImGui@@YA_JHM_J0_NMM@Z
	??$ScaleValueFromRatioT@_K_JN@ImGui@@YA_KHM_K0_NMM@Z
	??$SliderBehaviorT@HHM@ImGui@@YA_NAEBUImRect@@IHPEAHHHPEBDHPEAU1@@Z
	??$SliderBehaviorT@IHM@ImGui@@YA_NAEBUImRect@@IHPEAIIIPEBDHPEAU1@@Z
	??$SliderBehaviorT@MMM@ImGui@@YA_NAEBUImRect@@IHPEAMMMPEBDHPEAU1@@Z
	??$SliderBehaviorT@NNN@ImGui@@YA_NAEBUImRect@@IHPEANNNPEBDHPEAU1@@Z
	??$SliderBehaviorT@_J_JN@ImGui@@YA_NAEBUImRect@@IHPEA_J_J2PEBDHPEAU1@@Z
	??$SliderBehaviorT@_K_JN@ImGui@@YA_NAEBUImRect@@IHPEA_K_K2PEBDHPEAU1@@Z
	??$_Alloc_proxy@V?$allocator@U_Container_proxy@std@@@std@@@_Container_base12@std@@QEAAX$$QEAV?$allocator@U_Container_proxy@std@@@1@@Z
	??$_Allocate@$0BA@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
	??$_Allocate_at_least_helper@V?$allocator@D@std@@@std@@YAPEADAEAV?$allocator@D@0@AEA_K@Z
	??$_Allocate_for_capacity@$0A@@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CAPEADAEAV?$allocator@D@1@AEA_K@Z
	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
	??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z
	??$_Construct_in_place@PEADAEAPEAD@std@@YAXAEAPEAD0@Z
	??$_Construct_in_place@PEADAEBQEAD@std@@YAXAEAPEADAEBQEAD@Z
	??$_Construct_in_place@U_Container_proxy@std@@PEAU_Container_base12@2@@std@@YAXAEAU_Container_proxy@0@$$QEAPEAU_Container_base12@0@@Z
	??$_Convert_size@_K_K@std@@YA_K_K@Z
	??$_Deallocate@$0BA@@std@@YAXPEAX_K@Z
	??$_Deallocate_plain@V?$allocator@U_Container_proxy@std@@@std@@@std@@YAXAEAV?$allocator@U_Container_proxy@std@@@0@QEAU_Container_proxy@0@@Z
	??$_Delete_plain_internal@V?$allocator@U_Container_proxy@std@@@std@@@std@@YAXAEAV?$allocator@U_Container_proxy@std@@@0@QEAU_Container_proxy@0@@Z
	??$_Destroy_in_place@PEAD@std@@YAXAEAPEAD@Z
	??$_Destroy_range@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@YAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@QEAV10@AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@@Z
	??$_Get_size_of_n@$00@std@@YA_K_K@Z
	??$_Get_size_of_n@$0BA@@std@@YA_K_K@Z
	??$_Getvals@_W@?$time_get@DV?$istreambuf_iterator@DU?$char_traits@D@std@@@std@@@std@@IEAAX_WAEBV_Locinfo@1@@Z
	??$_Getvals@_W@?$time_get@_WV?$istreambuf_iterator@_WU?$char_traits@_W@std@@@std@@@std@@IEAAX_WAEBV_Locinfo@1@@Z
	??$_Max_limit@_J@std@@YA_JXZ
	??$_Reallocate_grow_by@V<lambda_319d5e083f45f90dcdce5dce53cbb275>@@D@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_319d5e083f45f90dcdce5dce53cbb275>@@D@Z
	??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z
	??$_Unfancy@D@std@@YAPEADPEAD@Z
	??$_Unfancy@U_Container_proxy@std@@@std@@YAPEAU_Container_proxy@0@PEAU10@@Z
	??$_Unfancy@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@YAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@PEAV10@@Z
	??$_Unfancy_maybe_null@VConfig@osgEarth@@@std@@YAPEAVConfig@osgEarth@@PEAV12@@Z
	??$addressof@PEAD@std@@YAPEAPEADAEAPEAD@Z
	??$addressof@U_Container_base12@std@@@std@@YAPEAU_Container_base12@0@AEAU10@@Z
	??$addressof@U_Container_proxy@std@@@std@@YAPEAU_Container_proxy@0@AEAU10@@Z
	??$addressof@V?$_String_val@U?$_Simple_types@D@std@@@std@@@std@@YAPEAV?$_String_val@U?$_Simple_types@D@std@@@0@AEAV10@@Z
	??$addressof@V?$basic_ostream@DU?$char_traits@D@std@@@std@@@std@@YAPEAV?$basic_ostream@DU?$char_traits@D@std@@@0@AEAV10@@Z
	??$destroy@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@?$_Default_allocator_traits@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@std@@SAXAEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@1@QEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@@Z
	??$endl@DU?$char_traits@D@std@@@std@@YAAEAV?$basic_ostream@DU?$char_traits@D@std@@@0@AEAV10@@Z
	??$exchange@PEAU_Container_proxy@std@@$$T@std@@YAPEAU_Container_proxy@0@AEAPEAU10@$$QEA$$T@Z
	??$exchange@PEAU_Iterator_base12@std@@$$T@std@@YAPEAU_Iterator_base12@0@AEAPEAU10@$$QEA$$T@Z
	??$forward@AEAPEAD@std@@YAAEAPEADAEAPEAD@Z
	??$forward@AEBQEAD@std@@YAAEBQEADAEBQEAD@Z
	??$forward@PEAU_Container_base12@std@@@std@@YA$$QEAPEAU_Container_base12@0@AEAPEAU10@@Z
	??$forward@V?$allocator@D@std@@@std@@YA$$QEAV?$allocator@D@0@AEAV10@@Z
	??$max@_K@std@@YAAEB_KAEB_K0@Z
	??$min@_K@std@@YAAEB_KAEB_K0@Z
	??$move@AEAV?$allocator@D@std@@@std@@YA$$QEAV?$allocator@D@0@AEAV10@@Z
	??$move@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@YA$$QEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@AEAV10@@Z
	??0<unnamed-type-BoxSelector>@ImClickInteractionState@@QEAA@XZ
	??0<unnamed-type-LayoutStyle>@ImNodeData@@QEAA@XZ
	??0<unnamed-type-LinkCreation>@ImClickInteractionState@@QEAA@XZ
	??0?$ImBitArray@$0CJK@$0A@@@QEAA@XZ
	??0?$ImBitArray@$0JK@$0?CAA@@@QEAA@XZ
	??0?$ImChunkStream@UImGuiTableSettings@@@@QEAA@XZ
	??0?$ImChunkStream@UImGuiWindowSettings@@@@QEAA@XZ
	??0?$ImObjectPool@UImLinkData@@@@QEAA@XZ
	??0?$ImObjectPool@UImNodeData@@@@QEAA@XZ
	??0?$ImObjectPool@UImPinData@@@@QEAA@XZ
	??0?$ImPool@UImGuiDockContextPruneNodeData@@@@QEAA@XZ
	??0?$ImPool@UImGuiTabBar@@@@QEAA@XZ
	??0?$ImPool@UImGuiTable@@@@QEAA@XZ
	??0?$ImSpan@F@@QEAA@XZ
	??0?$ImSpan@UImGuiTableCellData@@@@QEAA@XZ
	??0?$ImSpan@UImGuiTableColumn@@@@QEAA@XZ
	??0?$ImSpanAllocator@$05@@QEAA@XZ
	??0?$ImVector@D@@QEAA@XZ
	??0?$ImVector@E@@QEAA@XZ
	??0?$ImVector@G@@QEAA@AEBU0@@Z
	??0?$ImVector@G@@QEAA@XZ
	??0?$ImVector@H@@QEAA@XZ
	??0?$ImVector@I@@QEAA@XZ
	??0?$ImVector@M@@QEAA@XZ
	??0?$ImVector@PEAD@@QEAA@XZ
	??0?$ImVector@PEAUImDrawList@@@@QEAA@XZ
	??0?$ImVector@PEAUImFont@@@@QEAA@XZ
	??0?$ImVector@PEAUImGuiDockNode@@@@QEAA@XZ
	??0?$ImVector@PEAUImGuiViewport@@@@QEAA@XZ
	??0?$ImVector@PEAUImGuiViewportP@@@@QEAA@XZ
	??0?$ImVector@PEAUImGuiWindow@@@@QEAA@XZ
	??0?$ImVector@PEAUMyDocument@@@@QEAA@XZ
	??0?$ImVector@PEAX@@QEAA@XZ
	??0?$ImVector@PEBD@@QEAA@XZ
	??0?$ImVector@UImDrawChannel@@@@QEAA@XZ
	??0?$ImVector@UImDrawCmd@@@@QEAA@XZ
	??0?$ImVector@UImDrawVert@@@@QEAA@XZ
	??0?$ImVector@UImFontAtlasCustomRect@@@@QEAA@XZ
	??0?$ImVector@UImFontBuildDstData@@@@QEAA@XZ
	??0?$ImVector@UImFontBuildSrcData@@@@QEAA@XZ
	??0?$ImVector@UImFontConfig@@@@QEAA@XZ
	??0?$ImVector@UImFontGlyph@@@@QEAA@XZ
	??0?$ImVector@UImGuiColorMod@@@@QEAA@XZ
	??0?$ImVector@UImGuiContextHook@@@@QEAA@XZ
	??0?$ImVector@UImGuiDockContextPruneNodeData@@@@QEAA@XZ
	??0?$ImVector@UImGuiDockNodeSettings@@@@QEAA@XZ
	??0?$ImVector@UImGuiDockRequest@@@@QEAA@XZ
	??0?$ImVector@UImGuiFocusScopeData@@@@QEAA@XZ
	??0?$ImVector@UImGuiGroupData@@@@QEAA@XZ
	??0?$ImVector@UImGuiInputEvent@@@@QEAA@XZ
	??0?$ImVector@UImGuiKeyRoutingData@@@@QEAA@XZ
	??0?$ImVector@UImGuiListClipperData@@@@QEAA@XZ
	??0?$ImVector@UImGuiListClipperRange@@@@QEAA@XZ
	??0?$ImVector@UImGuiNavTreeNodeData@@@@QEAA@XZ
	??0?$ImVector@UImGuiOldColumnData@@@@QEAA@XZ
	??0?$ImVector@UImGuiOldColumns@@@@QEAA@XZ
	??0?$ImVector@UImGuiPlatformMonitor@@@@QEAA@XZ
	??0?$ImVector@UImGuiPopupData@@@@QEAA@XZ
	??0?$ImVector@UImGuiPtrOrIndex@@@@QEAA@XZ
	??0?$ImVector@UImGuiSettingsHandler@@@@QEAA@XZ
	??0?$ImVector@UImGuiShrinkWidthItem@@@@QEAA@XZ
	??0?$ImVector@UImGuiStackLevelInfo@@@@QEAA@XZ
	??0?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAA@XZ
	??0?$ImVector@UImGuiStyleMod@@@@QEAA@XZ
	??0?$ImVector@UImGuiTabBar@@@@QEAA@XZ
	??0?$ImVector@UImGuiTabItem@@@@QEAA@XZ
	??0?$ImVector@UImGuiTable@@@@QEAA@XZ
	??0?$ImVector@UImGuiTableColumnSortSpecs@@@@QEAA@XZ
	??0?$ImVector@UImGuiTableInstanceData@@@@QEAA@XZ
	??0?$ImVector@UImGuiTableTempData@@@@QEAA@XZ
	??0?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEAA@XZ
	??0?$ImVector@UImGuiWindowStackData@@@@QEAA@XZ
	??0?$ImVector@UImLinkData@@@@QEAA@XZ
	??0?$ImVector@UImNodeData@@@@QEAA@XZ
	??0?$ImVector@UImNodesColElement@@@@QEAA@XZ
	??0?$ImVector@UImNodesStyleVarElement@@@@QEAA@XZ
	??0?$ImVector@UImPinData@@@@QEAA@XZ
	??0?$ImVector@UImVec2@@@@QEAA@XZ
	??0?$ImVector@UImVec4@@@@QEAA@XZ
	??0?$ImVector@UMyDocument@@@@QEAA@XZ
	??0?$ImVector@Ustbrp_rect@@@@QEAA@XZ
	??0?$ImVector@Ustbtt_packedchar@@@@QEAA@XZ
	??0?$ImVector@_N@@QEAA@XZ
	??0?$_Container_proxy_ptr12@V?$allocator@U_Container_proxy@std@@@std@@@std@@QEAA@AEAV?$allocator@U_Container_proxy@std@@@1@AEAU_Container_base12@1@@Z
	??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ
	??0?$allocator@D@std@@QEAA@XZ
	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@$$QEAV01@@Z
	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@U_String_constructor_concat_tag@1@AEBV01@QEBD_K23@Z
	??0EmulateThreeButtonMouse@ImNodesIO@@QEAA@XZ
	??0ExampleAppConsole@@QEAA@XZ
	??0ExampleAppDocuments@@QEAA@XZ
	??0ExampleAppLog@@QEAA@XZ
	??0ImClickInteractionState@@QEAA@XZ
	??0ImColor@@QEAA@AEBUImVec4@@@Z
	??0ImColor@@QEAA@MMMM@Z
	??0ImDrawChannel@@QEAA@XZ
	??0ImDrawCmd@@QEAA@XZ
	??0ImDrawCmdHeader@@QEAA@XZ
	??0ImDrawData@@QEAA@XZ
	??0ImDrawDataBuilder@@QEAA@XZ
	??0ImDrawList@@QEAA@PEAUImDrawListSharedData@@@Z
	??0ImDrawListSharedData@@QEAA@XZ
	??0ImDrawListSplitter@@QEAA@XZ
	??0ImFont@@QEAA@XZ
	??0ImFontAtlas@@QEAA@XZ
	??0ImFontAtlasCustomRect@@QEAA@XZ
	??0ImFontConfig@@QEAA@XZ
	??0ImGuiColorMod@@QEAA@XZ
	??0ImGuiComboPreviewData@@QEAA@XZ
	??0ImGuiContext@@QEAA@PEAUImFontAtlas@@@Z
	??0ImGuiDebugAllocInfo@@QEAA@XZ
	??0ImGuiDockContext@@QEAA@XZ
	??0ImGuiDockContextPruneNodeData@@QEAA@XZ
	??0ImGuiDockNode@@QEAA@I@Z
	??0ImGuiDockNodeSettings@@QEAA@XZ
	??0ImGuiDockNodeTreeInfo@@QEAA@XZ
	??0ImGuiDockPreviewData@@QEAA@XZ
	??0ImGuiDockRequest@@QEAA@XZ
	??0ImGuiIDStackTool@@QEAA@XZ
	??0ImGuiIO@@QEAA@AEBU0@@Z
	??0ImGuiIO@@QEAA@XZ
	??0ImGuiInputEvent@@QEAA@XZ
	??0ImGuiInputTextCallbackData@@QEAA@XZ
	??0ImGuiInputTextDeactivatedState@@QEAA@XZ
	??0ImGuiInputTextState@@QEAA@XZ
	??0ImGuiKeyOwnerData@@QEAA@XZ
	??0ImGuiKeyRoutingData@@QEAA@XZ
	??0ImGuiKeyRoutingTable@@QEAA@XZ
	??0ImGuiLastItemData@@QEAA@XZ
	??0ImGuiListClipper@@QEAA@XZ
	??0ImGuiListClipperData@@QEAA@XZ
	??0ImGuiMenuColumns@@QEAA@XZ
	??0ImGuiMetricsConfig@@QEAA@XZ
	??0ImGuiNavItemData@@QEAA@XZ
	??0ImGuiNextItemData@@QEAA@XZ
	??0ImGuiNextWindowData@@QEAA@XZ
	??0ImGuiOldColumnData@@QEAA@XZ
	??0ImGuiOldColumns@@QEAA@XZ
	??0ImGuiPayload@@QEAA@XZ
	??0ImGuiPlatformIO@@QEAA@XZ
	??0ImGuiPlatformImeData@@QEAA@XZ
	??0ImGuiPlatformMonitor@@QEAA@XZ
	??0ImGuiPlotArrayGetterData@@QEAA@PEBMH@Z
	??0ImGuiPopupData@@QEAA@XZ
	??0ImGuiPtrOrIndex@@QEAA@H@Z
	??0ImGuiPtrOrIndex@@QEAA@PEAX@Z
	??0ImGuiSettingsHandler@@QEAA@XZ
	??0ImGuiSizeCallbackData@@QEAA@XZ
	??0ImGuiStackLevelInfo@@QEAA@XZ
	??0ImGuiStackSizes@@QEAA@XZ
	??0ImGuiStorage@@QEAA@XZ
	??0ImGuiStoragePair@ImGuiStorage@@QEAA@IH@Z
	??0ImGuiStoragePair@ImGuiStorage@@QEAA@IM@Z
	??0ImGuiStoragePair@ImGuiStorage@@QEAA@IPEAX@Z
	??0ImGuiStyle@@QEAA@XZ
	??0ImGuiStyleMod@@QEAA@HM@Z
	??0ImGuiStyleMod@@QEAA@HUImVec2@@@Z
	??0ImGuiTabBar@@QEAA@XZ
	??0ImGuiTabBarSection@@QEAA@XZ
	??0ImGuiTabItem@@QEAA@XZ
	??0ImGuiTable@@QEAA@XZ
	??0ImGuiTableColumn@@QEAA@XZ
	??0ImGuiTableColumnSettings@@QEAA@XZ
	??0ImGuiTableColumnSortSpecs@@QEAA@XZ
	??0ImGuiTableInstanceData@@QEAA@XZ
	??0ImGuiTableSettings@@QEAA@XZ
	??0ImGuiTableSortSpecs@@QEAA@XZ
	??0ImGuiTableTempData@@QEAA@XZ
	??0ImGuiTextBuffer@@QEAA@XZ
	??0ImGuiTextFilter@@QEAA@PEBD@Z
	??0ImGuiTextIndex@@QEAA@XZ
	??0ImGuiTextRange@ImGuiTextFilter@@QEAA@PEBD0@Z
	??0ImGuiTypingSelectState@@QEAA@XZ
	??0ImGuiViewport@@QEAA@XZ
	??0ImGuiViewportP@@QEAA@XZ
	??0ImGuiWindow@@QEAA@PEAUImGuiContext@@PEBD@Z
	??0ImGuiWindowClass@@QEAA@XZ
	??0ImGuiWindowSettings@@QEAA@XZ
	??0ImGuiWindowStackData@@QEAA@XZ
	??0ImGuiWindowTempData@@QEAA@XZ
	??0ImGui_ImplOpenGL3_Data@@QEAA@XZ
	??0ImLinkData@@QEAA@H@Z
	??0ImNodeData@@QEAA@H@Z
	??0ImNodesColElement@@QEAA@IH@Z
	??0ImNodesContext@@QEAA@XZ
	??0ImNodesEditorContext@@QEAA@XZ
	??0ImNodesIO@@QEAA@XZ
	??0ImNodesStyle@@QEAA@XZ
	??0ImNodesStyleVarElement@@QEAA@HM@Z
	??0ImNodesStyleVarElement@@QEAA@HUImVec2@@@Z
	??0ImOptionalIndex@@QEAA@H@Z
	??0ImOptionalIndex@@QEAA@XZ
	??0ImPinData@@QEAA@H@Z
	??0ImRect@@QEAA@AEBUImVec2@@0@Z
	??0ImRect@@QEAA@AEBUImVec4@@@Z
	??0ImRect@@QEAA@MMMM@Z
	??0ImRect@@QEAA@XZ
	??0ImVec1@@QEAA@XZ
	??0ImVec2@@QEAA@MM@Z
	??0ImVec2@@QEAA@XZ
	??0ImVec2ih@@QEAA@AEBUImVec2@@@Z
	??0ImVec2ih@@QEAA@FF@Z
	??0ImVec2ih@@QEAA@XZ
	??0ImVec4@@QEAA@MMMM@Z
	??0ImVec4@@QEAA@XZ
	??0LinkDetachWithModifierClick@ImNodesIO@@QEAA@XZ
	??0MultipleSelectModifier@ImNodesIO@@QEAA@XZ
	??0MyDocument@@QEAA@PEBD_NAEBUImVec4@@@Z
	??0Vec3f@osg@@QEAA@MMM@Z
	??0_Basic_container_proxy_ptr12@std@@IEAA@XZ
	??0_Bxty@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ
	??0_Container_base12@std@@QEAA@XZ
	??0_Container_proxy@std@@QEAA@PEAU_Container_base12@1@@Z
	??0_Sentry_base@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@AEAV12@@Z
	??0bad_alloc@std@@AEAA@QEBD@Z
	??0bad_alloc@std@@QEAA@AEBV01@@Z
	??0bad_array_new_length@std@@QEAA@AEBV01@@Z
	??0bad_array_new_length@std@@QEAA@XZ
	??0exception@std@@QEAA@AEBV01@@Z
	??0exception@std@@QEAA@QEBDH@Z
	??0sentry@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@AEAV12@@Z
	??1?$ImChunkStream@UImGuiTableSettings@@@@QEAA@XZ
	??1?$ImChunkStream@UImGuiWindowSettings@@@@QEAA@XZ
	??1?$ImObjectPool@UImLinkData@@@@QEAA@XZ
	??1?$ImObjectPool@UImNodeData@@@@QEAA@XZ
	??1?$ImObjectPool@UImPinData@@@@QEAA@XZ
	??1?$ImPool@UImGuiDockContextPruneNodeData@@@@QEAA@XZ
	??1?$ImPool@UImGuiTabBar@@@@QEAA@XZ
	??1?$ImPool@UImGuiTable@@@@QEAA@XZ
	??1?$ImVector@D@@QEAA@XZ
	??1?$ImVector@E@@QEAA@XZ
	??1?$ImVector@G@@QEAA@XZ
	??1?$ImVector@H@@QEAA@XZ
	??1?$ImVector@I@@QEAA@XZ
	??1?$ImVector@M@@QEAA@XZ
	??1?$ImVector@PEAD@@QEAA@XZ
	??1?$ImVector@PEAUImDrawList@@@@QEAA@XZ
	??1?$ImVector@PEAUImFont@@@@QEAA@XZ
	??1?$ImVector@PEAUImGuiDockNode@@@@QEAA@XZ
	??1?$ImVector@PEAUImGuiViewport@@@@QEAA@XZ
	??1?$ImVector@PEAUImGuiViewportP@@@@QEAA@XZ
	??1?$ImVector@PEAUImGuiWindow@@@@QEAA@XZ
	??1?$ImVector@PEAUMyDocument@@@@QEAA@XZ
	??1?$ImVector@PEAX@@QEAA@XZ
	??1?$ImVector@PEBD@@QEAA@XZ
	??1?$ImVector@UImDrawChannel@@@@QEAA@XZ
	??1?$ImVector@UImDrawCmd@@@@QEAA@XZ
	??1?$ImVector@UImDrawVert@@@@QEAA@XZ
	??1?$ImVector@UImFontAtlasCustomRect@@@@QEAA@XZ
	??1?$ImVector@UImFontBuildDstData@@@@QEAA@XZ
	??1?$ImVector@UImFontBuildSrcData@@@@QEAA@XZ
	??1?$ImVector@UImFontConfig@@@@QEAA@XZ
	??1?$ImVector@UImFontGlyph@@@@QEAA@XZ
	??1?$ImVector@UImGuiColorMod@@@@QEAA@XZ
	??1?$ImVector@UImGuiContextHook@@@@QEAA@XZ
	??1?$ImVector@UImGuiDockContextPruneNodeData@@@@QEAA@XZ
	??1?$ImVector@UImGuiDockNodeSettings@@@@QEAA@XZ
	??1?$ImVector@UImGuiDockRequest@@@@QEAA@XZ
	??1?$ImVector@UImGuiFocusScopeData@@@@QEAA@XZ
	??1?$ImVector@UImGuiGroupData@@@@QEAA@XZ
	??1?$ImVector@UImGuiInputEvent@@@@QEAA@XZ
	??1?$ImVector@UImGuiKeyRoutingData@@@@QEAA@XZ
	??1?$ImVector@UImGuiListClipperData@@@@QEAA@XZ
	??1?$ImVector@UImGuiListClipperRange@@@@QEAA@XZ
	??1?$ImVector@UImGuiNavTreeNodeData@@@@QEAA@XZ
	??1?$ImVector@UImGuiOldColumnData@@@@QEAA@XZ
	??1?$ImVector@UImGuiOldColumns@@@@QEAA@XZ
	??1?$ImVector@UImGuiPlatformMonitor@@@@QEAA@XZ
	??1?$ImVector@UImGuiPopupData@@@@QEAA@XZ
	??1?$ImVector@UImGuiPtrOrIndex@@@@QEAA@XZ
	??1?$ImVector@UImGuiSettingsHandler@@@@QEAA@XZ
	??1?$ImVector@UImGuiShrinkWidthItem@@@@QEAA@XZ
	??1?$ImVector@UImGuiStackLevelInfo@@@@QEAA@XZ
	??1?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAA@XZ
	??1?$ImVector@UImGuiStyleMod@@@@QEAA@XZ
	??1?$ImVector@UImGuiTabBar@@@@QEAA@XZ
	??1?$ImVector@UImGuiTabItem@@@@QEAA@XZ
	??1?$ImVector@UImGuiTable@@@@QEAA@XZ
	??1?$ImVector@UImGuiTableColumnSortSpecs@@@@QEAA@XZ
	??1?$ImVector@UImGuiTableInstanceData@@@@QEAA@XZ
	??1?$ImVector@UImGuiTableTempData@@@@QEAA@XZ
	??1?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEAA@XZ
	??1?$ImVector@UImGuiWindowStackData@@@@QEAA@XZ
	??1?$ImVector@UImLinkData@@@@QEAA@XZ
	??1?$ImVector@UImNodeData@@@@QEAA@XZ
	??1?$ImVector@UImNodesColElement@@@@QEAA@XZ
	??1?$ImVector@UImNodesStyleVarElement@@@@QEAA@XZ
	??1?$ImVector@UImPinData@@@@QEAA@XZ
	??1?$ImVector@UImVec2@@@@QEAA@XZ
	??1?$ImVector@UImVec4@@@@QEAA@XZ
	??1?$ImVector@UMyDocument@@@@QEAA@XZ
	??1?$ImVector@Ustbrp_rect@@@@QEAA@XZ
	??1?$ImVector@Ustbtt_packedchar@@@@QEAA@XZ
	??1?$ImVector@_N@@QEAA@XZ
	??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ
	??1?$_Container_proxy_ptr12@V?$allocator@U_Container_proxy@std@@@std@@@std@@QEAA@XZ
	??1?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ
	??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
	??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
	??1ExampleAppConsole@@QEAA@XZ
	??1ExampleAppDocuments@@QEAA@XZ
	??1ExampleAppLog@@QEAA@XZ
	??1ImBitVector@@QEAA@XZ
	??1ImDrawData@@QEAA@XZ
	??1ImDrawDataBuilder@@QEAA@XZ
	??1ImDrawList@@QEAA@XZ
	??1ImDrawListSharedData@@QEAA@XZ
	??1ImDrawListSplitter@@QEAA@XZ
	??1ImFont@@QEAA@XZ
	??1ImFontAtlas@@QEAA@XZ
	??1ImFontBuildSrcData@@QEAA@XZ
	??1ImGuiContext@@QEAA@XZ
	??1ImGuiDockContext@@QEAA@XZ
	??1ImGuiDockNode@@QEAA@XZ
	??1ImGuiDockPreviewData@@QEAA@XZ
	??1ImGuiIDStackTool@@QEAA@XZ
	??1ImGuiIO@@QEAA@XZ
	??1ImGuiInputTextDeactivatedState@@QEAA@XZ
	??1ImGuiInputTextState@@QEAA@XZ
	??1ImGuiKeyRoutingTable@@QEAA@XZ
	??1ImGuiListClipper@@QEAA@XZ
	??1ImGuiListClipperData@@QEAA@XZ
	??1ImGuiOldColumns@@QEAA@XZ
	??1ImGuiPlatformIO@@QEAA@XZ
	??1ImGuiStorage@@QEAA@XZ
	??1ImGuiTabBar@@QEAA@XZ
	??1ImGuiTable@@QEAA@XZ
	??1ImGuiTableTempData@@QEAA@XZ
	??1ImGuiTextBuffer@@QEAA@XZ
	??1ImGuiTextFilter@@QEAA@XZ
	??1ImGuiTextIndex@@QEAA@XZ
	??1ImGuiViewport@@QEAA@XZ
	??1ImGuiViewportP@@QEAA@XZ
	??1ImGuiWindow@@QEAA@XZ
	??1ImGuiWindowTempData@@QEAA@XZ
	??1ImNodeData@@QEAA@XZ
	??1ImNodesContext@@QEAA@XZ
	??1ImNodesEditorContext@@QEAA@XZ
	??1_Bxty@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ
	??1_Sentry_base@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@XZ
	??1bad_alloc@std@@UEAA@XZ
	??1bad_array_new_length@std@@UEAA@XZ
	??1exception@std@@UEAA@XZ
	??1sentry@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAA@XZ
	??2@YAPEAX_KPEAX@Z
	??2@YAPEAX_KUImNewWrapper@@PEAX@Z
	??3@YAXPEAXUImNewWrapper@@0@Z
	??4?$ImVector@G@@QEAAAEAU0@AEBU0@@Z
	??4?$ImVector@UImDrawCmd@@@@QEAAAEAU0@AEBU0@@Z
	??4?$ImVector@UImDrawVert@@@@QEAAAEAU0@AEBU0@@Z
	??4ImOptionalIndex@@QEAAAEAU0@H@Z
	??8ImOptionalIndex@@QEBA_NAEBU0@@Z
	??8ImOptionalIndex@@QEBA_NH@Z
	??A?$ImSpan@F@@QEAAAEAFH@Z
	??A?$ImSpan@UImGuiTableCellData@@@@QEAAAEAUImGuiTableCellData@@H@Z
	??A?$ImSpan@UImGuiTableColumn@@@@QEAAAEAUImGuiTableColumn@@H@Z
	??A?$ImSpan@UImGuiTableColumn@@@@QEBAAEBUImGuiTableColumn@@H@Z
	??A?$ImVector@D@@QEAAAEADH@Z
	??A?$ImVector@D@@QEBAAEBDH@Z
	??A?$ImVector@G@@QEAAAEAGH@Z
	??A?$ImVector@G@@QEBAAEBGH@Z
	??A?$ImVector@H@@QEAAAEAHH@Z
	??A?$ImVector@H@@QEBAAEBHH@Z
	??A?$ImVector@I@@QEAAAEAIH@Z
	??A?$ImVector@I@@QEBAAEBIH@Z
	??A?$ImVector@M@@QEAAAEAMH@Z
	??A?$ImVector@M@@QEBAAEBMH@Z
	??A?$ImVector@PEAD@@QEAAAEAPEADH@Z
	??A?$ImVector@PEAUImDrawList@@@@QEAAAEAPEAUImDrawList@@H@Z
	??A?$ImVector@PEAUImFont@@@@QEAAAEAPEAUImFont@@H@Z
	??A?$ImVector@PEAUImGuiDockNode@@@@QEAAAEAPEAUImGuiDockNode@@H@Z
	??A?$ImVector@PEAUImGuiViewport@@@@QEAAAEAPEAUImGuiViewport@@H@Z
	??A?$ImVector@PEAUImGuiViewportP@@@@QEAAAEAPEAUImGuiViewportP@@H@Z
	??A?$ImVector@PEAUImGuiWindow@@@@QEAAAEAPEAUImGuiWindow@@H@Z
	??A?$ImVector@PEAUMyDocument@@@@QEAAAEAPEAUMyDocument@@H@Z
	??A?$ImVector@PEBD@@QEAAAEAPEBDH@Z
	??A?$ImVector@UImDrawChannel@@@@QEAAAEAUImDrawChannel@@H@Z
	??A?$ImVector@UImDrawCmd@@@@QEAAAEAUImDrawCmd@@H@Z
	??A?$ImVector@UImDrawCmd@@@@QEBAAEBUImDrawCmd@@H@Z
	??A?$ImVector@UImDrawVert@@@@QEAAAEAUImDrawVert@@H@Z
	??A?$ImVector@UImFontAtlasCustomRect@@@@QEAAAEAUImFontAtlasCustomRect@@H@Z
	??A?$ImVector@UImFontBuildDstData@@@@QEAAAEAUImFontBuildDstData@@H@Z
	??A?$ImVector@UImFontBuildSrcData@@@@QEAAAEAUImFontBuildSrcData@@H@Z
	??A?$ImVector@UImFontConfig@@@@QEAAAEAUImFontConfig@@H@Z
	??A?$ImVector@UImFontGlyph@@@@QEAAAEAUImFontGlyph@@H@Z
	??A?$ImVector@UImGuiContextHook@@@@QEAAAEAUImGuiContextHook@@H@Z
	??A?$ImVector@UImGuiDockContextPruneNodeData@@@@QEAAAEAUImGuiDockContextPruneNodeData@@H@Z
	??A?$ImVector@UImGuiDockNodeSettings@@@@QEAAAEAUImGuiDockNodeSettings@@H@Z
	??A?$ImVector@UImGuiFocusScopeData@@@@QEAAAEAUImGuiFocusScopeData@@H@Z
	??A?$ImVector@UImGuiInputEvent@@@@QEAAAEAUImGuiInputEvent@@H@Z
	??A?$ImVector@UImGuiKeyRoutingData@@@@QEAAAEAUImGuiKeyRoutingData@@H@Z
	??A?$ImVector@UImGuiListClipperData@@@@QEAAAEAUImGuiListClipperData@@H@Z
	??A?$ImVector@UImGuiListClipperRange@@@@QEAAAEAUImGuiListClipperRange@@H@Z
	??A?$ImVector@UImGuiOldColumnData@@@@QEAAAEAUImGuiOldColumnData@@H@Z
	??A?$ImVector@UImGuiOldColumns@@@@QEAAAEAUImGuiOldColumns@@H@Z
	??A?$ImVector@UImGuiPlatformMonitor@@@@QEAAAEAUImGuiPlatformMonitor@@H@Z
	??A?$ImVector@UImGuiPopupData@@@@QEAAAEAUImGuiPopupData@@H@Z
	??A?$ImVector@UImGuiShrinkWidthItem@@@@QEAAAEAUImGuiShrinkWidthItem@@H@Z
	??A?$ImVector@UImGuiStackLevelInfo@@@@QEAAAEAUImGuiStackLevelInfo@@H@Z
	??A?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAAAEAUImGuiStoragePair@ImGuiStorage@@H@Z
	??A?$ImVector@UImGuiTabBar@@@@QEAAAEAUImGuiTabBar@@H@Z
	??A?$ImVector@UImGuiTabItem@@@@QEAAAEAUImGuiTabItem@@H@Z
	??A?$ImVector@UImGuiTable@@@@QEAAAEAUImGuiTable@@H@Z
	??A?$ImVector@UImGuiTableInstanceData@@@@QEAAAEAUImGuiTableInstanceData@@H@Z
	??A?$ImVector@UImGuiTableTempData@@@@QEAAAEAUImGuiTableTempData@@H@Z
	??A?$ImVector@UImLinkData@@@@QEAAAEAUImLinkData@@H@Z
	??A?$ImVector@UImLinkData@@@@QEBAAEBUImLinkData@@H@Z
	??A?$ImVector@UImNodeData@@@@QEAAAEAUImNodeData@@H@Z
	??A?$ImVector@UImNodeData@@@@QEBAAEBUImNodeData@@H@Z
	??A?$ImVector@UImPinData@@@@QEAAAEAUImPinData@@H@Z
	??A?$ImVector@UImPinData@@@@QEBAAEBUImPinData@@H@Z
	??A?$ImVector@UImVec2@@@@QEAAAEAUImVec2@@H@Z
	??A?$ImVector@UMyDocument@@@@QEAAAEAUMyDocument@@H@Z
	??A?$ImVector@Ustbrp_rect@@@@QEAAAEAUstbrp_rect@@H@Z
	??A?$ImVector@Ustbtt_packedchar@@@@QEAAAEAUstbtt_packedchar@@H@Z
	??A?$ImVector@_N@@QEAAAEA_NH@Z
	??A?$ImVector@_N@@QEBAAEB_NH@Z
	??A?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAAAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@1@_K@Z
	??AImGuiTextBuffer@@QEBADH@Z
	??AImVec2@@QEAAAEAM_K@Z
	??AImVec2@@QEBAM_K@Z
	??B?$function@$$A6AXXZ@std@@QEBA_NXZ
	??BImColor@@QEBA?AUImVec4@@XZ
	??BImColor@@QEBAIXZ
	??Bsentry@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEBA_NXZ
	??R<lambda_319d5e083f45f90dcdce5dce53cbb275>@@QEBA@QEADQEBD_KD@Z
	??R<lambda_65e615be2a453ca0576c979606f46740>@@QEBA@QEADQEBD_K12@Z
	??R?$_Func_class@X$$V@std@@QEBAXXZ
	??_H@YAXPEAX_K1P6APEAX0@Z@Z
	?AcceptDragDropPayload@ImGui@@YAPEBUImGuiPayload@@PEBDH@Z
	?ActivateItemByID@ImGui@@YAXI@Z
	?Add@?$ImPool@UImGuiDockContextPruneNodeData@@@@QEAAPEAUImGuiDockContextPruneNodeData@@XZ
	?Add@?$ImPool@UImGuiTabBar@@@@QEAAPEAUImGuiTabBar@@XZ
	?Add@?$ImPool@UImGuiTable@@@@QEAAPEAUImGuiTable@@XZ
	?Add@ImRect@@QEAAXAEBU1@@Z
	?Add@ImRect@@QEAAXAEBUImVec2@@@Z
	?AddBezierCubic@ImDrawList@@QEAAXAEBUImVec2@@000IMH@Z
	?AddBezierQuadratic@ImDrawList@@QEAAXAEBUImVec2@@00IMH@Z
	?AddCallback@ImDrawList@@QEAAXP6AXPEBU1@PEBUImDrawCmd@@@ZPEAX@Z
	?AddChar@ImFontGlyphRangesBuilder@@QEAAXG@Z
	?AddCircle@ImDrawList@@QEAAXAEBUImVec2@@MIHM@Z
	?AddCircleFilled@ImDrawList@@QEAAXAEBUImVec2@@MIH@Z
	?AddContextHook@ImGui@@YAIPEAUImGuiContext@@PEBUImGuiContextHook@@@Z
	?AddConvexPolyFilled@ImDrawList@@QEAAXPEBUImVec2@@HI@Z
	?AddCustomRectFontGlyph@ImFontAtlas@@QEAAHPEAUImFont@@GHHMAEBUImVec2@@@Z
	?AddCustomRectRegular@ImFontAtlas@@QEAAHHH@Z
	?AddDrawCmd@ImDrawList@@QEAAXXZ
	?AddDrawList@ImDrawData@@QEAAXPEAUImDrawList@@@Z
	?AddDrawListToDrawDataEx@ImGui@@YAXPEAUImDrawData@@PEAU?$ImVector@PEAUImDrawList@@@@PEAUImDrawList@@@Z
	?AddEllipse@ImDrawList@@QEAAXAEBUImVec2@@MMIMHM@Z
	?AddEllipseFilled@ImDrawList@@QEAAXAEBUImVec2@@MMIMH@Z
	?AddFocusEvent@ImGuiIO@@QEAAX_N@Z
	?AddFont@ImFontAtlas@@QEAAPEAUImFont@@PEBUImFontConfig@@@Z
	?AddFontDefault@ImFontAtlas@@QEAAPEAUImFont@@PEBUImFontConfig@@@Z
	?AddFontFromFileTTF@ImFontAtlas@@QEAAPEAUImFont@@PEBDMPEBUImFontConfig@@PEBG@Z
	?AddFontFromMemoryCompressedBase85TTF@ImFontAtlas@@QEAAPEAUImFont@@PEBDMPEBUImFontConfig@@PEBG@Z
	?AddFontFromMemoryCompressedTTF@ImFontAtlas@@QEAAPEAUImFont@@PEBXHMPEBUImFontConfig@@PEBG@Z
	?AddFontFromMemoryTTF@ImFontAtlas@@QEAAPEAUImFont@@PEAXHMPEBUImFontConfig@@PEBG@Z
	?AddGlyph@ImFont@@QEAAXPEBUImFontConfig@@GMMMMMMMMM@Z
	?AddImage@ImDrawList@@QEAAXPEAXAEBUImVec2@@111I@Z
	?AddImageQuad@ImDrawList@@QEAAXPEAXAEBUImVec2@@1111111I@Z
	?AddImageRounded@ImDrawList@@QEAAXPEAXAEBUImVec2@@111IMH@Z
	?AddInputCharacter@ImGuiIO@@QEAAXI@Z
	?AddInputCharacterUTF16@ImGuiIO@@QEAAXG@Z
	?AddInputCharactersUTF8@ImGuiIO@@QEAAXPEBD@Z
	?AddKeyAnalogEvent@ImGuiIO@@QEAAXW4ImGuiKey@@_NM@Z
	?AddKeyEvent@ImGuiIO@@QEAAXW4ImGuiKey@@_N@Z
	?AddLine@ImDrawList@@QEAAXAEBUImVec2@@0IM@Z
	?AddLog@ExampleAppConsole@@QEAAXPEBDZZ
	?AddLog@ExampleAppLog@@QEAAXPEBDZZ
	?AddMouseButtonEvent@ImGuiIO@@QEAAXH_N@Z
	?AddMousePosEvent@ImGuiIO@@QEAAXMM@Z
	?AddMouseSourceEvent@ImGuiIO@@QEAAXW4ImGuiMouseSource@@@Z
	?AddMouseViewportEvent@ImGuiIO@@QEAAXI@Z
	?AddMouseWheelEvent@ImGuiIO@@QEAAXMM@Z
	?AddNgon@ImDrawList@@QEAAXAEBUImVec2@@MIHM@Z
	?AddNgonFilled@ImDrawList@@QEAAXAEBUImVec2@@MIH@Z
	?AddPolyline@ImDrawList@@QEAAXPEBUImVec2@@HIHM@Z
	?AddQuad@ImDrawList@@QEAAXAEBUImVec2@@000IM@Z
	?AddQuadFilled@ImDrawList@@QEAAXAEBUImVec2@@000I@Z
	?AddRanges@ImFontGlyphRangesBuilder@@QEAAXPEBG@Z
	?AddRect@ImDrawList@@QEAAXAEBUImVec2@@0IMHM@Z
	?AddRectFilled@ImDrawList@@QEAAXAEBUImVec2@@0IMH@Z
	?AddRectFilledMultiColor@ImDrawList@@QEAAXAEBUImVec2@@0IIII@Z
	?AddRemapChar@ImFont@@QEAAXGG_N@Z
	?AddSettingsHandler@ImGui@@YAXPEBUImGuiSettingsHandler@@@Z
	?AddText@ImDrawList@@QEAAXAEBUImVec2@@IPEBD1@Z
	?AddText@ImDrawList@@QEAAXPEBUImFont@@MAEBUImVec2@@IPEBD2MPEBUImVec4@@@Z
	?AddText@ImFontGlyphRangesBuilder@@QEAAXPEBD0@Z
	?AddTriangle@ImDrawList@@QEAAXAEBUImVec2@@00IM@Z
	?AddTriangleFilled@ImDrawList@@QEAAXAEBUImVec2@@00I@Z
	?AlignTextToFramePadding@ImGui@@YAXXZ
	?ArrowButton@ImGui@@YA_NPEBDH@Z
	?ArrowButtonEx@ImGui@@YA_NPEBDHUImVec2@@H@Z
	?Begin@ImGui@@YA_NPEBDPEA_NH@Z
	?Begin@ImGuiListClipper@@QEAAXHM@Z
	?BeginChild@ImGui@@YA_NIAEBUImVec2@@HH@Z
	?BeginChild@ImGui@@YA_NPEBDAEBUImVec2@@HH@Z
	?BeginChildEx@ImGui@@YA_NPEBDIAEBUImVec2@@HH@Z
	?BeginColumns@ImGui@@YAXPEBDHH@Z
	?BeginCombo@ImGui@@YA_NPEBD0H@Z
	?BeginComboPopup@ImGui@@YA_NIAEBUImRect@@H@Z
	?BeginComboPreview@ImGui@@YA_NXZ
	?BeginDisabled@ImGui@@YAX_N@Z
	?BeginDockableDragDropSource@ImGui@@YAXPEAUImGuiWindow@@@Z
	?BeginDockableDragDropTarget@ImGui@@YAXPEAUImGuiWindow@@@Z
	?BeginDocked@ImGui@@YAXPEAUImGuiWindow@@PEA_N@Z
	?BeginDragDropSource@ImGui@@YA_NH@Z
	?BeginDragDropTarget@ImGui@@YA_NXZ
	?BeginDragDropTargetCustom@ImGui@@YA_NAEBUImRect@@I@Z
	?BeginGroup@ImGui@@YAXXZ
	?BeginInputAttribute@ImNodes@@YAXHH@Z
	?BeginItemTooltip@ImGui@@YA_NXZ
	?BeginListBox@ImGui@@YA_NPEBDAEBUImVec2@@@Z
	?BeginMainMenuBar@ImGui@@YA_NXZ
	?BeginMenu@ImGui@@YA_NPEBD_N@Z
	?BeginMenuBar@ImGui@@YA_NXZ
	?BeginMenuEx@ImGui@@YA_NPEBD0_N@Z
	?BeginNode@ImNodes@@YAXH@Z
	?BeginNodeEditor@ImNodes@@YAXXZ
	?BeginNodeTitleBar@ImNodes@@YAXXZ
	?BeginOutputAttribute@ImNodes@@YAXHH@Z
	?BeginPopup@ImGui@@YA_NPEBDH@Z
	?BeginPopupContextItem@ImGui@@YA_NPEBDH@Z
	?BeginPopupContextVoid@ImGui@@YA_NPEBDH@Z
	?BeginPopupContextWindow@ImGui@@YA_NPEBDH@Z
	?BeginPopupEx@ImGui@@YA_NIH@Z
	?BeginPopupModal@ImGui@@YA_NPEBDPEA_NH@Z
	?BeginStaticAttribute@ImNodes@@YAXH@Z
	?BeginTabBar@ImGui@@YA_NPEBDH@Z
	?BeginTabBarEx@ImGui@@YA_NPEAUImGuiTabBar@@AEBUImRect@@H@Z
	?BeginTabItem@ImGui@@YA_NPEBDPEA_NH@Z
	?BeginTable@ImGui@@YA_NPEBDHHAEBUImVec2@@M@Z
	?BeginTableEx@ImGui@@YA_NPEBDIHHAEBUImVec2@@M@Z
	?BeginTooltip@ImGui@@YA_NXZ
	?BeginTooltipEx@ImGui@@YA_NHH@Z
	?BeginTooltipHidden@ImGui@@YA_NXZ
	?BeginViewportSideBar@ImGui@@YA_NPEBDPEAUImGuiViewport@@HMH@Z
	?BringWindowToDisplayBack@ImGui@@YAXPEAUImGuiWindow@@@Z
	?BringWindowToDisplayBehind@ImGui@@YAXPEAUImGuiWindow@@0@Z
	?BringWindowToDisplayFront@ImGui@@YAXPEAUImGuiWindow@@@Z
	?BringWindowToFocusFront@ImGui@@YAXPEAUImGuiWindow@@@Z
	?Build@ImFontAtlas@@QEAA_NXZ
	?Build@ImGuiTextFilter@@QEAAXXZ
	?BuildLookupTable@ImFont@@QEAAXXZ
	?BuildRanges@ImFontGlyphRangesBuilder@@QEAAXPEAU?$ImVector@G@@@Z
	?BuildSortByKey@ImGuiStorage@@QEAAXXZ
	?Bullet@ImGui@@YAXXZ
	?BulletText@ImGui@@YAXPEBDZZ
	?BulletTextV@ImGui@@YAXPEBDPEAD@Z
	?Button@ImGui@@YA_NPEBDAEBUImVec2@@@Z
	?ButtonBehavior@ImGui@@YA_NAEBUImRect@@IPEA_N1H@Z
	?ButtonEx@ImGui@@YA_NPEBDAEBUImVec2@@H@Z
	?CalcCustomRectUV@ImFontAtlas@@QEBAXPEBUImFontAtlasCustomRect@@PEAUImVec2@@1@Z
	?CalcFontSize@ImGuiWindow@@QEBAMXZ
	?CalcItemSize@ImGui@@YA?AUImVec2@@U2@MM@Z
	?CalcItemWidth@ImGui@@YAMXZ
	?CalcNextTotalWidth@ImGuiMenuColumns@@QEAAX_N@Z
	?CalcRoundingFlagsForRectInRect@ImGui@@YAHAEBUImRect@@0M@Z
	?CalcTextSize@ImGui@@YA?AUImVec2@@PEBD0_NM@Z
	?CalcTextSizeA@ImFont@@QEBA?AUImVec2@@MMMPEBD0PEAPEBD@Z
	?CalcTypematicRepeatAmount@ImGui@@YAHMMMM@Z
	?CalcWindowNextAutoFitSize@ImGui@@YA?AUImVec2@@PEAUImGuiWindow@@@Z
	?CalcWordWrapPositionA@ImFont@@QEBAPEBDMPEBD0M@Z
	?CalcWorkRectPos@ImGuiViewportP@@QEBA?AUImVec2@@AEBU2@@Z
	?CalcWorkRectSize@ImGuiViewportP@@QEBA?AUImVec2@@AEBU2@0@Z
	?CalcWrapWidthForPos@ImGui@@YAMAEBUImVec2@@M@Z
	?CallContextHooks@ImGui@@YAXPEAUImGuiContext@@W4ImGuiContextHookType@@@Z
	?ChannelsMerge@ImDrawList@@QEAAXXZ
	?ChannelsSetCurrent@ImDrawList@@QEAAXH@Z
	?ChannelsSplit@ImDrawList@@QEAAXH@Z
	?Checkbox@ImGui@@YA_NPEBDPEA_N@Z
	?CheckboxFlags@ImGui@@YA_NPEBDPEAHH@Z
	?CheckboxFlags@ImGui@@YA_NPEBDPEAII@Z
	?CheckboxFlags@ImGui@@YA_NPEBDPEA_J_J@Z
	?CheckboxFlags@ImGui@@YA_NPEBDPEA_K_K@Z
	?Clear@?$ImPool@UImGuiDockContextPruneNodeData@@@@QEAAXXZ
	?Clear@?$ImPool@UImGuiTabBar@@@@QEAAXXZ
	?Clear@?$ImPool@UImGuiTable@@@@QEAAXXZ
	?Clear@ExampleAppLog@@QEAAXXZ
	?Clear@ImBitVector@@QEAAXXZ
	?Clear@ImDrawData@@QEAAXXZ
	?Clear@ImDrawListSplitter@@QEAAXXZ
	?Clear@ImFontAtlas@@QEAAXXZ
	?Clear@ImGuiKeyRoutingTable@@QEAAXXZ
	?Clear@ImGuiNavItemData@@QEAAXXZ
	?Clear@ImGuiPayload@@QEAAXXZ
	?Clear@ImGuiStorage@@QEAAXXZ
	?Clear@ImGuiTypingSelectState@@QEAAXXZ
	?ClearActiveID@ImGui@@YAXXZ
	?ClearAllBits@?$ImBitArray@$0CJK@$0A@@@QEAAXXZ
	?ClearAllBits@?$ImBitArray@$0JK@$0?CAA@@@QEAAXXZ
	?ClearDragDrop@ImGui@@YAXXZ
	?ClearEventsQueue@ImGuiIO@@QEAAXXZ
	?ClearFlags@ImGuiNextItemData@@QEAAXXZ
	?ClearFlags@ImGuiNextWindowData@@QEAAXXZ
	?ClearFonts@ImFontAtlas@@QEAAXXZ
	?ClearFreeMemory@ImDrawListSplitter@@QEAAXXZ
	?ClearFreeMemory@ImGuiInputTextDeactivatedState@@QEAAXXZ
	?ClearFreeMemory@ImGuiInputTextState@@QEAAXXZ
	?ClearIniSettings@ImGui@@YAXXZ
	?ClearInputCharacters@ImGuiIO@@QEAAXXZ
	?ClearInputData@ImFontAtlas@@QEAAXXZ
	?ClearInputKeys@ImGuiIO@@QEAAXXZ
	?ClearLinkSelection@ImNodes@@YAXH@Z
	?ClearLinkSelection@ImNodes@@YAXXZ
	?ClearLog@ExampleAppConsole@@QEAAXXZ
	?ClearNodeSelection@ImNodes@@YAXH@Z
	?ClearNodeSelection@ImNodes@@YAXXZ
	?ClearOutputData@ImFont@@QEAAXXZ
	?ClearRequestFlags@ImGuiViewportP@@QEAAXXZ
	?ClearSelection@ImGuiInputTextState@@QEAAXXZ
	?ClearTexData@ImFontAtlas@@QEAAXXZ
	?ClearWindowSettings@ImGui@@YAXPEBD@Z
	?ClipWith@ImRect@@QEAAXAEBU1@@Z
	?ClipWithFull@ImRect@@QEAAXAEBU1@@Z
	?CloneOutput@ImDrawList@@QEBAPEAU1@XZ
	?CloseButton@ImGui@@YA_NIAEBUImVec2@@@Z
	?CloseCurrentPopup@ImGui@@YAXXZ
	?ClosePopupToLevel@ImGui@@YAXH_N@Z
	?ClosePopupsExceptModals@ImGui@@YAXXZ
	?ClosePopupsOverWindow@ImGui@@YAXPEAUImGuiWindow@@_N@Z
	?CollapseButton@ImGui@@YA_NIAEBUImVec2@@PEAUImGuiDockNode@@@Z
	?CollapsingHeader@ImGui@@YA_NPEBDH@Z
	?CollapsingHeader@ImGui@@YA_NPEBDPEA_NH@Z
	?ColorButton@ImGui@@YA_NPEBDAEBUImVec4@@HAEBUImVec2@@@Z
	?ColorConvertFloat4ToU32@ImGui@@YAIAEBUImVec4@@@Z
	?ColorConvertHSVtoRGB@ImGui@@YAXMMMAEAM00@Z
	?ColorConvertRGBtoHSV@ImGui@@YAXMMMAEAM00@Z
	?ColorConvertU32ToFloat4@ImGui@@YA?AUImVec4@@I@Z
	?ColorEdit3@ImGui@@YA_NPEBDQEAMH@Z
	?ColorEdit4@ImGui@@YA_NPEBDQEAMH@Z
	?ColorEditOptionsPopup@ImGui@@YAXPEBMH@Z
	?ColorPicker3@ImGui@@YA_NPEBDQEAMH@Z
	?ColorPicker4@ImGui@@YA_NPEBDQEAMHPEBM@Z
	?ColorPickerOptionsPopup@ImGui@@YAXPEBMH@Z
	?ColorTooltip@ImGui@@YAXPEBDPEBMH@Z
	?Columns@ImGui@@YAXHPEBD_N@Z
	?Combo@ImGui@@YA_NPEBDPEAH0H@Z
	?Combo@ImGui@@YA_NPEBDPEAHP6APEBDPEAXH@Z2HH@Z
	?Combo@ImGui@@YA_NPEBDPEAHP6A_NPEAXHPEAPEBD@Z2HH@Z
	?Combo@ImGui@@YA_NPEBDPEAHQEBQEBDHH@Z
	?CompareWithContextState@ImGuiStackSizes@@QEAAXPEAUImGuiContext@@@Z
	?Contains@?$ImPool@UImGuiTabBar@@@@QEBA_NPEBUImGuiTabBar@@@Z
	?Contains@ImRect@@QEBA_NAEBU1@@Z
	?Contains@ImRect@@QEBA_NAEBUImVec2@@@Z
	?ContainsWithPad@ImRect@@QEBA_NAEBUImVec2@@0@Z
	?ConvertShortcutMod@ImGui@@YAHH@Z
	?ConvertSingleModFlagToKey@ImGui@@YA?AW4ImGuiKey@@PEAUImGuiContext@@W42@@Z
	?Create@ImBitVector@@QEAAXH@Z
	?CreateContext@ImGui@@YAPEAUImGuiContext@@PEAUImFontAtlas@@@Z
	?CreateContext@ImNodes@@YAPEAUImNodesContext@@XZ
	?CreateNewWindowSettings@ImGui@@YAPEAUImGuiWindowSettings@@PEBD@Z
	?CursorAnimReset@ImGuiInputTextState@@QEAAXXZ
	?CursorClamp@ImGuiInputTextState@@QEAAXXZ
	?DataTypeApplyFromText@ImGui@@YA_NPEBDHPEAX0@Z
	?DataTypeApplyOp@ImGui@@YAXHHPEAXPEBX1@Z
	?DataTypeClamp@ImGui@@YA_NHPEAXPEBX1@Z
	?DataTypeCompare@ImGui@@YAHHPEBX0@Z
	?DataTypeFormatString@ImGui@@YAHPEADHHPEBXPEBD@Z
	?DataTypeGetInfo@ImGui@@YAPEBUImGuiDataTypeInfo@@H@Z
	?DeIndexAllBuffers@ImDrawData@@QEAAXXZ
	?DebugAllocHook@ImGui@@YAXPEAUImGuiDebugAllocInfo@@HPEAX_K@Z
	?DebugBreakButton@ImGui@@YA_NPEBD0@Z
	?DebugBreakButtonTooltip@ImGui@@YAX_NPEBD@Z
	?DebugBreakClearData@ImGui@@YAXXZ
	?DebugCheckVersionAndDataLayout@ImGui@@YA_NPEBD_K11111@Z
	?DebugDrawCursorPos@ImGui@@YAXI@Z
	?DebugDrawItemRect@ImGui@@YAXI@Z
	?DebugDrawLineExtents@ImGui@@YAXI@Z
	?DebugFlashStyleColor@ImGui@@YAXH@Z
	?DebugHookIdInfo@ImGui@@YAXIHPEBX0@Z
	?DebugLocateItem@ImGui@@YAXI@Z
	?DebugLocateItemOnHover@ImGui@@YAXI@Z
	?DebugLocateItemResolveWithLastItem@ImGui@@YAXXZ
	?DebugLog@ImGui@@YAXPEBDZZ
	?DebugLogV@ImGui@@YAXPEBDPEAD@Z
	?DebugNodeColumns@ImGui@@YAXPEAUImGuiOldColumns@@@Z
	?DebugNodeDockNode@ImGui@@YAXPEAUImGuiDockNode@@PEBD@Z
	?DebugNodeDrawCmdShowMeshAndBoundingBox@ImGui@@YAXPEAUImDrawList@@PEBU2@PEBUImDrawCmd@@_N3@Z
	?DebugNodeDrawList@ImGui@@YAXPEAUImGuiWindow@@PEAUImGuiViewportP@@PEBUImDrawList@@PEBD@Z
	?DebugNodeFont@ImGui@@YAXPEAUImFont@@@Z
	?DebugNodeFontGlyph@ImGui@@YAXPEAUImFont@@PEBUImFontGlyph@@@Z
	?DebugNodeInputTextState@ImGui@@YAXPEAUImGuiInputTextState@@@Z
	?DebugNodeStorage@ImGui@@YAXPEAUImGuiStorage@@PEBD@Z
	?DebugNodeTabBar@ImGui@@YAXPEAUImGuiTabBar@@PEBD@Z
	?DebugNodeTable@ImGui@@YAXPEAUImGuiTable@@@Z
	?DebugNodeTableSettings@ImGui@@YAXPEAUImGuiTableSettings@@@Z
	?DebugNodeTypingSelectState@ImGui@@YAXPEAUImGuiTypingSelectState@@@Z
	?DebugNodeViewport@ImGui@@YAXPEAUImGuiViewportP@@@Z
	?DebugNodeWindow@ImGui@@YAXPEAUImGuiWindow@@PEBD@Z
	?DebugNodeWindowSettings@ImGui@@YAXPEAUImGuiWindowSettings@@@Z
	?DebugNodeWindowsList@ImGui@@YAXPEAU?$ImVector@PEAUImGuiWindow@@@@PEBD@Z
	?DebugNodeWindowsListByBeginStackParent@ImGui@@YAXPEAPEAUImGuiWindow@@HPEAU2@@Z
	?DebugRenderKeyboardPreview@ImGui@@YAXPEAUImDrawList@@@Z
	?DebugRenderViewportThumbnail@ImGui@@YAXPEAUImDrawList@@PEAUImGuiViewportP@@AEBUImRect@@@Z
	?DebugStartItemPicker@ImGui@@YAXXZ
	?DebugTextEncoding@ImGui@@YAXPEBD@Z
	?DeclColumns@ImGuiMenuColumns@@QEAAMMMMM@Z
	?DeleteChars@ImGuiInputTextCallbackData@@QEAAXHH@Z
	?DestroyContext@ImGui@@YAXPEAUImGuiContext@@@Z
	?DestroyContext@ImNodes@@YAXPEAUImNodesContext@@@Z
	?DestroyPlatformWindow@ImGui@@YAXPEAUImGuiViewportP@@@Z
	?DestroyPlatformWindows@ImGui@@YAXXZ
	?DisplayContents@MyDocument@@SAXPEAU1@@Z
	?DisplayContextMenu@MyDocument@@SAXPEAU1@@Z
	?DoForceClose@MyDocument@@QEAAXXZ
	?DoOpen@MyDocument@@QEAAXXZ
	?DoQueueClose@MyDocument@@QEAAXXZ
	?DoSave@MyDocument@@QEAAXXZ
	?DockBuilderAddNode@ImGui@@YAIIH@Z
	?DockBuilderCopyDockSpace@ImGui@@YAXIIPEAU?$ImVector@PEBD@@@Z
	?DockBuilderCopyNode@ImGui@@YAXIIPEAU?$ImVector@I@@@Z
	?DockBuilderCopyWindowSettings@ImGui@@YAXPEBD0@Z
	?DockBuilderDockWindow@ImGui@@YAXPEBDI@Z
	?DockBuilderFinish@ImGui@@YAXI@Z
	?DockBuilderGetCentralNode@ImGui@@YAPEAUImGuiDockNode@@I@Z
	?DockBuilderGetNode@ImGui@@YAPEAUImGuiDockNode@@I@Z
	?DockBuilderRemoveNode@ImGui@@YAXI@Z
	?DockBuilderRemoveNodeChildNodes@ImGui@@YAXI@Z
	?DockBuilderRemoveNodeDockedWindows@ImGui@@YAXI_N@Z
	?DockBuilderSetNodePos@ImGui@@YAXIUImVec2@@@Z
	?DockBuilderSetNodeSize@ImGui@@YAXIUImVec2@@@Z
	?DockBuilderSplitNode@ImGui@@YAIIHMPEAI0@Z
	?DockContextCalcDropPosForDocking@ImGui@@YA_NPEAUImGuiWindow@@PEAUImGuiDockNode@@01H_NPEAUImVec2@@@Z
	?DockContextClearNodes@ImGui@@YAXPEAUImGuiContext@@I_N@Z
	?DockContextEndFrame@ImGui@@YAXPEAUImGuiContext@@@Z
	?DockContextFindNodeByID@ImGui@@YAPEAUImGuiDockNode@@PEAUImGuiContext@@I@Z
	?DockContextGenNodeID@ImGui@@YAIPEAUImGuiContext@@@Z
	?DockContextInitialize@ImGui@@YAXPEAUImGuiContext@@@Z
	?DockContextNewFrameUpdateDocking@ImGui@@YAXPEAUImGuiContext@@@Z
	?DockContextNewFrameUpdateUndocking@ImGui@@YAXPEAUImGuiContext@@@Z
	?DockContextProcessUndockNode@ImGui@@YAXPEAUImGuiContext@@PEAUImGuiDockNode@@@Z
	?DockContextProcessUndockWindow@ImGui@@YAXPEAUImGuiContext@@PEAUImGuiWindow@@_N@Z
	?DockContextQueueDock@ImGui@@YAXPEAUImGuiContext@@PEAUImGuiWindow@@PEAUImGuiDockNode@@1HM_N@Z
	?DockContextQueueUndockNode@ImGui@@YAXPEAUImGuiContext@@PEAUImGuiDockNode@@@Z
	?DockContextQueueUndockWindow@ImGui@@YAXPEAUImGuiContext@@PEAUImGuiWindow@@@Z
	?DockContextRebuildNodes@ImGui@@YAXPEAUImGuiContext@@@Z
	?DockContextShutdown@ImGui@@YAXPEAUImGuiContext@@@Z
	?DockNodeBeginAmendTabBar@ImGui@@YA_NPEAUImGuiDockNode@@@Z
	?DockNodeEndAmendTabBar@ImGui@@YAXXZ
	?DockNodeGetDepth@ImGui@@YAHPEBUImGuiDockNode@@@Z
	?DockNodeGetRootNode@ImGui@@YAPEAUImGuiDockNode@@PEAU2@@Z
	?DockNodeIsInHierarchyOf@ImGui@@YA_NPEAUImGuiDockNode@@0@Z
	?DockNodeWindowMenuHandler_Default@ImGui@@YAXPEAUImGuiContext@@PEAUImGuiDockNode@@PEAUImGuiTabBar@@@Z
	?DockSpace@ImGui@@YAIIAEBUImVec2@@HPEBUImGuiWindowClass@@@Z
	?DockSpaceOverViewport@ImGui@@YAIPEBUImGuiViewport@@HPEBUImGuiWindowClass@@@Z
	?DragBehavior@ImGui@@YA_NIHPEAXMPEBX1PEBDH@Z
	?DragFloat2@ImGui@@YA_NPEBDQEAMMMM0H@Z
	?DragFloat3@ImGui@@YA_NPEBDQEAMMMM0H@Z
	?DragFloat4@ImGui@@YA_NPEBDQEAMMMM0H@Z
	?DragFloat@ImGui@@YA_NPEBDPEAMMMM0H@Z
	?DragFloatRange2@ImGui@@YA_NPEBDPEAM1MMM00H@Z
	?DragInt2@ImGui@@YA_NPEBDQEAHMHH0H@Z
	?DragInt3@ImGui@@YA_NPEBDQEAHMHH0H@Z
	?DragInt4@ImGui@@YA_NPEBDQEAHMHH0H@Z
	?DragInt@ImGui@@YA_NPEBDPEAHMHH0H@Z
	?DragIntRange2@ImGui@@YA_NPEBDPEAH1MHH00H@Z
	?DragScalar@ImGui@@YA_NPEBDHPEAXMPEBX20H@Z
	?DragScalarN@ImGui@@YA_NPEBDHPEAXHMPEBX20H@Z
	?Draw@ExampleAppConsole@@QEAAXPEBDPEA_N@Z
	?Draw@ExampleAppLog@@QEAAXPEBDPEA_N@Z
	?Draw@ImGuiTextFilter@@QEAA_NPEBDM@Z
	?Dummy@ImGui@@YAXAEBUImVec2@@@Z
	?EditorContextCreate@ImNodes@@YAPEAUImNodesEditorContext@@XZ
	?EditorContextFree@ImNodes@@YAXPEAUImNodesEditorContext@@@Z
	?EditorContextGetPanning@ImNodes@@YA?AUImVec2@@XZ
	?EditorContextMoveToNode@ImNodes@@YAXH@Z
	?EditorContextResetPanning@ImNodes@@YAXAEBUImVec2@@@Z
	?EditorContextSet@ImNodes@@YAXPEAUImNodesEditorContext@@@Z
	?End@ImGui@@YAXXZ
	?End@ImGuiListClipper@@QEAAXXZ
	?EndChild@ImGui@@YAXXZ
	?EndColumns@ImGui@@YAXXZ
	?EndCombo@ImGui@@YAXXZ
	?EndComboPreview@ImGui@@YAXXZ
	?EndDisabled@ImGui@@YAXXZ
	?EndDragDropSource@ImGui@@YAXXZ
	?EndDragDropTarget@ImGui@@YAXXZ
	?EndFrame@ImGui@@YAXXZ
	?EndGroup@ImGui@@YAXXZ
	?EndInputAttribute@ImNodes@@YAXXZ
	?EndListBox@ImGui@@YAXXZ
	?EndMainMenuBar@ImGui@@YAXXZ
	?EndMenu@ImGui@@YAXXZ
	?EndMenuBar@ImGui@@YAXXZ
	?EndNode@ImNodes@@YAXXZ
	?EndNodeEditor@ImNodes@@YAXXZ
	?EndNodeTitleBar@ImNodes@@YAXXZ
	?EndOutputAttribute@ImNodes@@YAXXZ
	?EndPopup@ImGui@@YAXXZ
	?EndStaticAttribute@ImNodes@@YAXXZ
	?EndTabBar@ImGui@@YAXXZ
	?EndTabItem@ImGui@@YAXXZ
	?EndTable@ImGui@@YAXXZ
	?EndTooltip@ImGui@@YAXXZ
	?ErrorCheckEndFrameRecover@ImGui@@YAXP6AXPEAXPEBDZZ0@Z
	?ErrorCheckEndWindowRecover@ImGui@@YAXP6AXPEAXPEBDZZ0@Z
	?ErrorCheckUsingSetCursorPosToExtendParentBoundaries@ImGui@@YAXXZ
	?ExecCommand@ExampleAppConsole@@QEAAXPEBD@Z
	?Expand@ImRect@@QEAAXAEBUImVec2@@@Z
	?Expand@ImRect@@QEAAXM@Z
	?FindBestWindowPosForPopup@ImGui@@YA?AUImVec2@@PEAUImGuiWindow@@@Z
	?FindBestWindowPosForPopupEx@ImGui@@YA?AUImVec2@@AEBU2@0PEAHAEBUImRect@@2W4ImGuiPopupPositionPolicy@@@Z
	?FindBlockingModal@ImGui@@YAPEAUImGuiWindow@@PEAU2@@Z
	?FindBottomMostVisibleWindowWithinBeginStack@ImGui@@YAPEAUImGuiWindow@@PEAU2@@Z
	?FindGlyph@ImFont@@QEBAPEBUImFontGlyph@@G@Z
	?FindGlyphNoFallback@ImFont@@QEBAPEBUImFontGlyph@@G@Z
	?FindHoveredViewportFromPlatformWindowStack@ImGui@@YAPEAUImGuiViewportP@@AEBUImVec2@@@Z
	?FindOrCreateColumns@ImGui@@YAPEAUImGuiOldColumns@@PEAUImGuiWindow@@I@Z
	?FindRenderedTextEnd@ImGui@@YAPEBDPEBD0@Z
	?FindSettingsHandler@ImGui@@YAPEAUImGuiSettingsHandler@@PEBD@Z
	?FindViewportByID@ImGui@@YAPEAUImGuiViewport@@I@Z
	?FindViewportByPlatformHandle@ImGui@@YAPEAUImGuiViewport@@PEAX@Z
	?FindWindowByID@ImGui@@YAPEAUImGuiWindow@@I@Z
	?FindWindowByName@ImGui@@YAPEAUImGuiWindow@@PEBD@Z
	?FindWindowDisplayIndex@ImGui@@YAHPEAUImGuiWindow@@@Z
	?FindWindowSettingsByID@ImGui@@YAPEAUImGuiWindowSettings@@I@Z
	?FindWindowSettingsByWindow@ImGui@@YAPEAUImGuiWindowSettings@@PEAUImGuiWindow@@@Z
	?FocusItem@ImGui@@YAXXZ
	?FocusTopMostWindowUnderOne@ImGui@@YAXPEAUImGuiWindow@@0PEAUImGuiViewport@@H@Z
	?FocusWindow@ImGui@@YAXPEAUImGuiWindow@@H@Z
	?FromIndices@ImGuiListClipperRange@@SA?AU1@HH@Z
	?FromPositions@ImGuiListClipperRange@@SA?AU1@MMHH@Z
	?GcAwakeTransientWindowBuffers@ImGui@@YAXPEAUImGuiWindow@@@Z
	?GcCompactTransientMiscBuffers@ImGui@@YAXXZ
	?GcCompactTransientWindowBuffers@ImGui@@YAXPEAUImGuiWindow@@@Z
	?GetAliveCount@?$ImPool@UImGuiTabBar@@@@QEBAHXZ
	?GetAliveCount@?$ImPool@UImGuiTable@@@@QEBAHXZ
	?GetAllocatorFunctions@ImGui@@YAXPEAP6APEAX_KPEAX@ZPEAP6AX11@ZPEAPEAX@Z
	?GetArea@ImRect@@QEBAMXZ
	?GetArenaSizeInBytes@?$ImSpanAllocator@$05@@QEAAHXZ
	?GetBL@ImRect@@QEBA?AUImVec2@@XZ
	?GetBR@ImRect@@QEBA?AUImVec2@@XZ
	?GetBackgroundDrawList@ImGui@@YAPEAUImDrawList@@PEAUImGuiViewport@@@Z
	?GetBackgroundDrawList@ImGui@@YAPEAUImDrawList@@XZ
	?GetBit@ImFontGlyphRangesBuilder@@QEBA_N_K@Z
	?GetBool@ImGuiStorage@@QEBA_NI_N@Z
	?GetBoolRef@ImGuiStorage@@QEAAPEA_NI_N@Z
	?GetBuildWorkRect@ImGuiViewportP@@QEBA?AUImRect@@XZ
	?GetByIndex@?$ImPool@UImGuiTabBar@@@@QEAAPEAUImGuiTabBar@@H@Z
	?GetByIndex@?$ImPool@UImGuiTable@@@@QEAAPEAUImGuiTable@@H@Z
	?GetByKey@?$ImPool@UImGuiDockContextPruneNodeData@@@@QEAAPEAUImGuiDockContextPruneNodeData@@I@Z
	?GetByKey@?$ImPool@UImGuiTable@@@@QEAAPEAUImGuiTable@@I@Z
	?GetCenter@ImGuiViewport@@QEBA?AUImVec2@@XZ
	?GetCenter@ImRect@@QEBA?AUImVec2@@XZ
	?GetCharAdvance@ImFont@@QEBAMG@Z
	?GetClipboardText@ImGui@@YAPEBDXZ
	?GetColorU32@ImGui@@YAIAEBUImVec4@@@Z
	?GetColorU32@ImGui@@YAIHM@Z
	?GetColorU32@ImGui@@YAII@Z
	?GetColumnIndex@ImGui@@YAHXZ
	?GetColumnNormFromOffset@ImGui@@YAMPEBUImGuiOldColumns@@M@Z
	?GetColumnOffset@ImGui@@YAMH@Z
	?GetColumnOffsetFromNorm@ImGui@@YAMPEBUImGuiOldColumns@@M@Z
	?GetColumnSettings@ImGuiTableSettings@@QEAAPEAUImGuiTableColumnSettings@@XZ
	?GetColumnWidth@ImGui@@YAMH@Z
	?GetColumnsCount@ImGui@@YAHXZ
	?GetColumnsID@ImGui@@YAIPEBDH@Z
	?GetContentRegionAvail@ImGui@@YA?AUImVec2@@XZ
	?GetContentRegionMax@ImGui@@YA?AUImVec2@@XZ
	?GetContentRegionMaxAbs@ImGui@@YA?AUImVec2@@XZ
	?GetCurrentContext@ImGui@@YAPEAUImGuiContext@@XZ
	?GetCurrentContext@ImNodes@@YAPEAUImNodesContext@@XZ
	?GetCurrentWindow@ImGui@@YAPEAUImGuiWindow@@XZ
	?GetCurrentWindowRead@ImGui@@YAPEAUImGuiWindow@@XZ
	?GetCursorPos@ImGui@@YA?AUImVec2@@XZ
	?GetCursorPosX@ImGui@@YAMXZ
	?GetCursorPosY@ImGui@@YAMXZ
	?GetCursorScreenPos@ImGui@@YA?AUImVec2@@XZ
	?GetCursorStartPos@ImGui@@YA?AUImVec2@@XZ
	?GetCustomRectByIndex@ImFontAtlas@@QEAAPEAUImFontAtlasCustomRect@@H@Z
	?GetDebugName@ImFont@@QEBAPEBDXZ
	?GetDefaultFont@ImGui@@YAPEAUImFont@@XZ
	?GetDragDropPayload@ImGui@@YAPEBUImGuiPayload@@XZ
	?GetDrawData@ImGui@@YAPEAUImDrawData@@XZ
	?GetDrawListSharedData@ImGui@@YAPEAUImDrawListSharedData@@XZ
	?GetFloat@ImGuiStorage@@QEBAMIM@Z
	?GetFloatRef@ImGuiStorage@@QEAAPEAMIM@Z
	?GetFont@ImGui@@YAPEAUImFont@@XZ
	?GetFontSize@ImGui@@YAMXZ
	?GetFontTexUvWhitePixel@ImGui@@YA?AUImVec2@@XZ
	?GetForegroundDrawList@ImGui@@YAPEAUImDrawList@@PEAUImGuiViewport@@@Z
	?GetForegroundDrawList@ImGui@@YAPEAUImDrawList@@PEAUImGuiWindow@@@Z
	?GetForegroundDrawList@ImGui@@YAPEAUImDrawList@@XZ
	?GetFrameCount@ImGui@@YAHXZ
	?GetFrameHeight@ImGui@@YAMXZ
	?GetFrameHeightWithSpacing@ImGui@@YAMXZ
	?GetGlyphRangesChineseFull@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesChineseSimplifiedCommon@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesCyrillic@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesDefault@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesGreek@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesJapanese@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesKorean@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesThai@ImFontAtlas@@QEAAPEBGXZ
	?GetGlyphRangesVietnamese@ImFontAtlas@@QEAAPEBGXZ
	?GetHeight@ImRect@@QEBAMXZ
	?GetHoveredID@ImGui@@YAIXZ
	?GetID@ImGui@@YAIPEBD0@Z
	?GetID@ImGui@@YAIPEBD@Z
	?GetID@ImGui@@YAIPEBX@Z
	?GetID@ImGuiWindow@@QEAAIH@Z
	?GetID@ImGuiWindow@@QEAAIPEBD0@Z
	?GetID@ImGuiWindow@@QEAAIPEBX@Z
	?GetIDFromRectangle@ImGuiWindow@@QEAAIAEBUImRect@@@Z
	?GetIDWithSeed@ImGui@@YAIHI@Z
	?GetIDWithSeed@ImGui@@YAIPEBD0I@Z
	?GetIO@ImGui@@YAAEAUImGuiIO@@XZ
	?GetIO@ImNodes@@YAAEAUImNodesIO@@XZ
	?GetIndex@?$ImPool@UImGuiTabBar@@@@QEBAHPEBUImGuiTabBar@@@Z
	?GetIndex@?$ImPool@UImGuiTable@@@@QEBAHPEBUImGuiTable@@@Z
	?GetInputTextState@ImGui@@YAPEAUImGuiInputTextState@@I@Z
	?GetInt@ImGuiStorage@@QEBAHIH@Z
	?GetIntRef@ImGuiStorage@@QEAAPEAHIH@Z
	?GetItemID@ImGui@@YAIXZ
	?GetItemRectMax@ImGui@@YA?AUImVec2@@XZ
	?GetItemRectMin@ImGui@@YA?AUImVec2@@XZ
	?GetItemRectSize@ImGui@@YA?AUImVec2@@XZ
	?GetKeyChordName@ImGui@@YAPEBDH@Z
	?GetKeyData@ImGui@@YAPEAUImGuiKeyData@@PEAUImGuiContext@@W4ImGuiKey@@@Z
	?GetKeyData@ImGui@@YAPEAUImGuiKeyData@@W4ImGuiKey@@@Z
	?GetKeyIndex@ImGui@@YA?AW4ImGuiKey@@W42@@Z
	?GetKeyMagnitude2d@ImGui@@YA?AUImVec2@@W4ImGuiKey@@000@Z
	?GetKeyName@ImGui@@YAPEBDW4ImGuiKey@@@Z
	?GetKeyOwner@ImGui@@YAIW4ImGuiKey@@@Z
	?GetKeyOwnerData@ImGui@@YAPEAUImGuiKeyOwnerData@@PEAUImGuiContext@@W4ImGuiKey@@@Z
	?GetKeyPressedAmount@ImGui@@YAHW4ImGuiKey@@MM@Z
	?GetMainRect@ImGuiViewportP@@QEBA?AUImRect@@XZ
	?GetMainViewport@ImGui@@YAPEAUImGuiViewport@@XZ
	?GetMapSize@?$ImPool@UImGuiTabBar@@@@QEBAHXZ
	?GetMapSize@?$ImPool@UImGuiTable@@@@QEBAHXZ
	?GetMouseClickedCount@ImGui@@YAHH@Z
	?GetMouseCursor@ImGui@@YAHXZ
	?GetMouseCursorTexData@ImFontAtlas@@QEAA_NHPEAUImVec2@@0QEAU2@1@Z
	?GetMouseDragDelta@ImGui@@YA?AUImVec2@@HM@Z
	?GetMousePos@ImGui@@YA?AUImVec2@@XZ
	?GetMousePosOnOpeningCurrentPopup@ImGui@@YA?AUImVec2@@XZ
	?GetName@ImGuiWindowSettings@@QEAAPEADXZ
	?GetNavTweakPressedAmount@ImGui@@YAMW4ImGuiAxis@@@Z
	?GetNodeDimensions@ImNodes@@YA?AUImVec2@@H@Z
	?GetNodeEditorSpacePos@ImNodes@@YA?AUImVec2@@H@Z
	?GetNodeGridSpacePos@ImNodes@@YA?AUImVec2@@H@Z
	?GetNodeScreenSpacePos@ImNodes@@YA?AUImVec2@@H@Z
	?GetOrAddByKey@?$ImPool@UImGuiDockContextPruneNodeData@@@@QEAAPEAUImGuiDockContextPruneNodeData@@I@Z
	?GetOrAddByKey@?$ImPool@UImGuiTabBar@@@@QEAAPEAUImGuiTabBar@@I@Z
	?GetOrAddByKey@?$ImPool@UImGuiTable@@@@QEAAPEAUImGuiTable@@I@Z
	?GetPlatformIO@ImGui@@YAAEAUImGuiPlatformIO@@XZ
	?GetPopupAllowedExtentRect@ImGui@@YA?AUImRect@@PEAUImGuiWindow@@@Z
	?GetScrollMaxX@ImGui@@YAMXZ
	?GetScrollMaxY@ImGui@@YAMXZ
	?GetScrollX@ImGui@@YAMXZ
	?GetScrollY@ImGui@@YAMXZ
	?GetSelectedLinks@ImNodes@@YAXPEAH@Z
	?GetSelectedNodes@ImNodes@@YAXPEAH@Z
	?GetShortcutRoutingData@ImGui@@YAPEAUImGuiKeyRoutingData@@H@Z
	?GetSize@ImRect@@QEBA?AUImVec2@@XZ
	?GetSpanPtrBegin@?$ImSpanAllocator@$05@@QEAAPEAXH@Z
	?GetSpanPtrEnd@?$ImSpanAllocator@$05@@QEAAPEAXH@Z
	?GetStateStorage@ImGui@@YAPEAUImGuiStorage@@XZ
	?GetStyle@ImGui@@YAAEAUImGuiStyle@@XZ
	?GetStyle@ImNodes@@YAAEAUImNodesStyle@@XZ
	?GetStyleColorName@ImGui@@YAPEBDH@Z
	?GetStyleColorVec4@ImGui@@YAAEBUImVec4@@H@Z
	?GetStyleVarInfo@ImGui@@YAPEBUImGuiDataVarInfo@@H@Z
	?GetTL@ImRect@@QEBA?AUImVec2@@XZ
	?GetTR@ImRect@@QEBA?AUImVec2@@XZ
	?GetTexDataAsAlpha8@ImFontAtlas@@QEAAXPEAPEAEPEAH11@Z
	?GetTexDataAsRGBA32@ImFontAtlas@@QEAAXPEAPEAEPEAH11@Z
	?GetTexID@ImDrawCmd@@QEBAPEAXXZ
	?GetTextLineHeight@ImGui@@YAMXZ
	?GetTextLineHeightWithSpacing@ImGui@@YAMXZ
	?GetTime@ImGui@@YANXZ
	?GetTopMostAndVisiblePopupModal@ImGui@@YAPEAUImGuiWindow@@XZ
	?GetTopMostPopupModal@ImGui@@YAPEAUImGuiWindow@@XZ
	?GetTreeNodeToLabelSpacing@ImGui@@YAMXZ
	?GetTypematicRepeatRate@ImGui@@YAXHPEAM0@Z
	?GetTypingSelectRequest@ImGui@@YAPEAUImGuiTypingSelectRequest@@H@Z
	?GetVarPtr@ImGuiDataVarInfo@@QEBAPEAXPEAX@Z
	?GetVarPtr@ImNodesStyleVarInfo@ImNodes@@QEBAPEAXPEAUImNodesStyle@@@Z
	?GetVersion@ImGui@@YAPEBDXZ
	?GetViewportPlatformMonitor@ImGui@@YAPEBUImGuiPlatformMonitor@@PEAUImGuiViewport@@@Z
	?GetVoidPtr@ImGuiStorage@@QEBAPEAXI@Z
	?GetVoidPtrRef@ImGuiStorage@@QEAAPEAPEAXIPEAX@Z
	?GetWidth@ImRect@@QEBAMXZ
	?GetWindowAlwaysWantOwnTabBar@ImGui@@YA_NPEAUImGuiWindow@@@Z
	?GetWindowContentRegionMax@ImGui@@YA?AUImVec2@@XZ
	?GetWindowContentRegionMin@ImGui@@YA?AUImVec2@@XZ
	?GetWindowDockID@ImGui@@YAIXZ
	?GetWindowDpiScale@ImGui@@YAMXZ
	?GetWindowDrawList@ImGui@@YAPEAUImDrawList@@XZ
	?GetWindowHeight@ImGui@@YAMXZ
	?GetWindowPos@ImGui@@YA?AUImVec2@@XZ
	?GetWindowResizeBorderID@ImGui@@YAIPEAUImGuiWindow@@H@Z
	?GetWindowResizeCornerID@ImGui@@YAIPEAUImGuiWindow@@H@Z
	?GetWindowScrollbarID@ImGui@@YAIPEAUImGuiWindow@@W4ImGuiAxis@@@Z
	?GetWindowScrollbarRect@ImGui@@YA?AUImRect@@PEAUImGuiWindow@@W4ImGuiAxis@@@Z
	?GetWindowSize@ImGui@@YA?AUImVec2@@XZ
	?GetWindowViewport@ImGui@@YAPEAUImGuiViewport@@XZ
	?GetWindowWidth@ImGui@@YAMXZ
	?GetWorkRect@ImGuiViewportP@@QEBA?AUImRect@@XZ
	?GrowIndex@ImFont@@QEAAXH@Z
	?HSV@ImColor@@SA?AU1@MMMM@Z
	?HasSelection@ImGuiInputTextState@@QEBA_NXZ
	?HasValue@ImOptionalIndex@@QEBA_NXZ
	?ImAlphaBlendColors@@YAIII@Z
	?ImBezierCubicCalc@@YA?AUImVec2@@AEBU1@000M@Z
	?ImBezierCubicClosestPoint@@YA?AUImVec2@@AEBU1@0000H@Z
	?ImBezierCubicClosestPointCasteljau@@YA?AUImVec2@@AEBU1@0000M@Z
	?ImBezierQuadraticCalc@@YA?AUImVec2@@AEBU1@00M@Z
	?ImBitArrayClearAllBits@@YAXPEAIH@Z
	?ImBitArrayClearBit@@YAXPEAIH@Z
	?ImBitArrayGetStorageSizeInBytes@@YA_KH@Z
	?ImBitArraySetBit@@YAXPEAIH@Z
	?ImBitArraySetBitRange@@YAXPEAIHH@Z
	?ImFileClose@@YA_NPEAU_iobuf@@@Z
	?ImFileGetSize@@YA_KPEAU_iobuf@@@Z
	?ImFileLoadToMemory@@YAPEAXPEBD0PEA_KH@Z
	?ImFileOpen@@YAPEAU_iobuf@@PEBD0@Z
	?ImFileRead@@YA_KPEAX_K1PEAU_iobuf@@@Z
	?ImFileWrite@@YA_KPEBX_K1PEAU_iobuf@@@Z
	?ImFontAtlasBuildFinish@@YAXPEAUImFontAtlas@@@Z
	?ImFontAtlasBuildInit@@YAXPEAUImFontAtlas@@@Z
	?ImFontAtlasBuildMultiplyCalcLookupTable@@YAXQEAEM@Z
	?ImFontAtlasBuildMultiplyRectAlpha8@@YAXQEBEPEAEHHHHH@Z
	?ImFontAtlasBuildPackCustomRects@@YAXPEAUImFontAtlas@@PEAX@Z
	?ImFontAtlasBuildRender32bppRectFromString@@YAXPEAUImFontAtlas@@HHHHPEBDDI@Z
	?ImFontAtlasBuildRender8bppRectFromString@@YAXPEAUImFontAtlas@@HHHHPEBDDE@Z
	?ImFontAtlasBuildSetupFont@@YAXPEAUImFontAtlas@@PEAUImFont@@PEAUImFontConfig@@MM@Z
	?ImFontAtlasGetBuilderForStbTruetype@@YAPEBUImFontBuilderIO@@XZ
	?ImFontAtlasUpdateConfigDataPointers@@YAXPEAUImFontAtlas@@@Z
	?ImFormatString@@YAHPEAD_KPEBDZZ
	?ImFormatStringToTempBuffer@@YAXPEAPEBD0PEBDZZ
	?ImFormatStringToTempBufferV@@YAXPEAPEBD0PEBDPEAD@Z
	?ImFormatStringV@@YAHPEAD_KPEBD0@Z
	?ImGui_ImplOpenGL3_CreateDeviceObjects@@YA_NXZ
	?ImGui_ImplOpenGL3_CreateFontsTexture@@YA_NXZ
	?ImGui_ImplOpenGL3_DestroyDeviceObjects@@YAXXZ
	?ImGui_ImplOpenGL3_DestroyFontsTexture@@YAXXZ
	?ImGui_ImplOpenGL3_Init@@YA_NPEBD@Z
	?ImGui_ImplOpenGL3_NewFrame@@YAXXZ
	?ImGui_ImplOpenGL3_RenderDrawData@@YAXPEAUImDrawData@@@Z
	?ImGui_ImplOpenGL3_Shutdown@@YAXXZ
	?ImHashData@@YAIPEBX_KI@Z
	?ImHashStr@@YAIPEBD_KI@Z
	?ImLineClosestPoint@@YA?AUImVec2@@AEBU1@00@Z
	?ImParseFormatFindEnd@@YAPEBDPEBD@Z
	?ImParseFormatFindStart@@YAPEBDPEBD@Z
	?ImParseFormatPrecision@@YAHPEBDH@Z
	?ImParseFormatSanitizeForPrinting@@YAXPEBDPEAD_K@Z
	?ImParseFormatSanitizeForScanning@@YAPEBDPEBDPEAD_K@Z
	?ImParseFormatTrimDecorations@@YAPEBDPEBDPEAD_K@Z
	?ImStrSkipBlank@@YAPEBDPEBD@Z
	?ImStrTrimBlanks@@YAXPEAD@Z
	?ImStrbolW@@YAPEBGPEBG0@Z
	?ImStrchrRange@@YAPEBDPEBD0D@Z
	?ImStrdup@@YAPEADPEBD@Z
	?ImStrdupcpy@@YAPEADPEADPEA_KPEBD@Z
	?ImStreolRange@@YAPEBDPEBD0@Z
	?ImStricmp@@YAHPEBD0@Z
	?ImStristr@@YAPEBDPEBD000@Z
	?ImStrlenW@@YAHPEBG@Z
	?ImStrncpy@@YAXPEADPEBD_K@Z
	?ImStrnicmp@@YAHPEBD0_K@Z
	?ImTextCharFromUtf8@@YAHPEAIPEBD1@Z
	?ImTextCharToUtf8@@YAPEBDQEADI@Z
	?ImTextCountCharsFromUtf8@@YAHPEBD0@Z
	?ImTextCountUtf8BytesFromChar@@YAHPEBD0@Z
	?ImTextCountUtf8BytesFromStr@@YAHPEBG0@Z
	?ImTextFindPreviousUtf8Codepoint@@YAPEBDPEBD0@Z
	?ImTextStrFromUtf8@@YAHPEAGHPEBD1PEAPEBD@Z
	?ImTextStrToUtf8@@YAHPEADHPEBG1@Z
	?ImTriangleArea@@YAMAEBUImVec2@@00@Z
	?ImTriangleBarycentricCoords@@YAXAEBUImVec2@@000AEAM11@Z
	?ImTriangleClosestPoint@@YA?AUImVec2@@AEBU1@000@Z
	?ImTriangleContainsPoint@@YA_NAEBUImVec2@@000@Z
	?Image@ImGui@@YAXPEAXAEBUImVec2@@11AEBUImVec4@@2@Z
	?ImageButton@ImGui@@YA_NPEAXAEBUImVec2@@11HAEBUImVec4@@2@Z
	?ImageButton@ImGui@@YA_NPEBDPEAXAEBUImVec2@@22AEBUImVec4@@3@Z
	?ImageButtonEx@ImGui@@YA_NIPEAXAEBUImVec2@@11AEBUImVec4@@2H@Z
	?IncludeItemsByIndex@ImGuiListClipper@@QEAAXHH@Z
	?Indent@ImGui@@YAXM@Z
	?Initialize@ImGui@@YAXXZ
	?InputDouble@ImGui@@YA_NPEBDPEANNN0H@Z
	?InputFloat2@ImGui@@YA_NPEBDQEAM0H@Z
	?InputFloat3@ImGui@@YA_NPEBDQEAM0H@Z
	?InputFloat4@ImGui@@YA_NPEBDQEAM0H@Z
	?InputFloat@ImGui@@YA_NPEBDPEAMMM0H@Z
	?InputInt2@ImGui@@YA_NPEBDQEAHH@Z
	?InputInt3@ImGui@@YA_NPEBDQEAHH@Z
	?InputInt4@ImGui@@YA_NPEBDQEAHH@Z
	?InputInt@ImGui@@YA_NPEBDPEAHHHH@Z
	?InputScalar@ImGui@@YA_NPEBDHPEAXPEBX20H@Z
	?InputScalarN@ImGui@@YA_NPEBDHPEAXHPEBX20H@Z
	?InputText@ImGui@@YA_NPEBDPEAD_KHP6AHPEAUImGuiInputTextCallbackData@@@ZPEAX@Z
	?InputTextDeactivateHook@ImGui@@YAXI@Z
	?InputTextEx@ImGui@@YA_NPEBD0PEADHAEBUImVec2@@HP6AHPEAUImGuiInputTextCallbackData@@@ZPEAX@Z
	?InputTextMultiline@ImGui@@YA_NPEBDPEAD_KAEBUImVec2@@HP6AHPEAUImGuiInputTextCallbackData@@@ZPEAX@Z
	?InputTextWithHint@ImGui@@YA_NPEBD0PEAD_KHP6AHPEAUImGuiInputTextCallbackData@@@ZPEAX@Z
	?InsertChars@ImGuiInputTextCallbackData@@QEAAXHPEBD0@Z
	?InvisibleButton@ImGui@@YA_NPEBDAEBUImVec2@@H@Z
	?IsActive@ImGuiTextFilter@@QEBA_NXZ
	?IsActiveIdUsingNavDir@ImGui@@YA_NH@Z
	?IsAliasKey@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsAnyAttributeActive@ImNodes@@YA_NPEAH@Z
	?IsAnyItemActive@ImGui@@YA_NXZ
	?IsAnyItemFocused@ImGui@@YA_NXZ
	?IsAnyItemHovered@ImGui@@YA_NXZ
	?IsAnyMouseDown@ImGui@@YA_NXZ
	?IsAttributeActive@ImNodes@@YA_NXZ
	?IsBuilt@ImFontAtlas@@QEBA_NXZ
	?IsCentralNode@ImGuiDockNode@@QEBA_NXZ
	?IsClippedEx@ImGui@@YA_NAEBUImRect@@I@Z
	?IsDataType@ImGuiPayload@@QEBA_NPEBD@Z
	?IsDelivery@ImGuiPayload@@QEBA_NXZ
	?IsDockSpace@ImGuiDockNode@@QEBA_NXZ
	?IsDragDropActive@ImGui@@YA_NXZ
	?IsDragDropPayloadBeingAccepted@ImGui@@YA_NXZ
	?IsEditorHovered@ImNodes@@YA_NXZ
	?IsEmpty@ImGuiDockNode@@QEBA_NXZ
	?IsFloatingNode@ImGuiDockNode@@QEBA_NXZ
	?IsGamepadKey@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsGlyphRangeUnused@ImFont@@QEAA_NII@Z
	?IsHiddenTabBar@ImGuiDockNode@@QEBA_NXZ
	?IsInverted@ImRect@@QEBA_NXZ
	?IsItemActivated@ImGui@@YA_NXZ
	?IsItemActive@ImGui@@YA_NXZ
	?IsItemClicked@ImGui@@YA_NH@Z
	?IsItemDeactivated@ImGui@@YA_NXZ
	?IsItemDeactivatedAfterEdit@ImGui@@YA_NXZ
	?IsItemEdited@ImGui@@YA_NXZ
	?IsItemFocused@ImGui@@YA_NXZ
	?IsItemHovered@ImGui@@YA_NH@Z
	?IsItemToggledOpen@ImGui@@YA_NXZ
	?IsItemToggledSelection@ImGui@@YA_NXZ
	?IsItemVisible@ImGui@@YA_NXZ
	?IsKeyChordPressed@ImGui@@YA_NH@Z
	?IsKeyChordPressed@ImGui@@YA_NHIH@Z
	?IsKeyDown@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsKeyDown@ImGui@@YA_NW4ImGuiKey@@I@Z
	?IsKeyPressed@ImGui@@YA_NW4ImGuiKey@@IH@Z
	?IsKeyPressed@ImGui@@YA_NW4ImGuiKey@@_N@Z
	?IsKeyReleased@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsKeyReleased@ImGui@@YA_NW4ImGuiKey@@I@Z
	?IsKeyboardKey@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsLeafNode@ImGuiDockNode@@QEBA_NXZ
	?IsLegacyKey@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsLinkCreated@ImNodes@@YA_NPEAH000PEA_N@Z
	?IsLinkCreated@ImNodes@@YA_NPEAH0PEA_N@Z
	?IsLinkDestroyed@ImNodes@@YA_NPEAH@Z
	?IsLinkDropped@ImNodes@@YA_NPEAH_N@Z
	?IsLinkHovered@ImNodes@@YA_NPEAH@Z
	?IsLinkSelected@ImNodes@@YA_NH@Z
	?IsLinkStarted@ImNodes@@YA_NPEAH@Z
	?IsLoaded@ImFont@@QEBA_NXZ
	?IsMouseClicked@ImGui@@YA_NHIH@Z
	?IsMouseClicked@ImGui@@YA_NH_N@Z
	?IsMouseDoubleClicked@ImGui@@YA_NH@Z
	?IsMouseDoubleClicked@ImGui@@YA_NHI@Z
	?IsMouseDown@ImGui@@YA_NH@Z
	?IsMouseDown@ImGui@@YA_NHI@Z
	?IsMouseDragPastThreshold@ImGui@@YA_NHM@Z
	?IsMouseDragging@ImGui@@YA_NHM@Z
	?IsMouseHoveringRect@ImGui@@YA_NAEBUImVec2@@0_N@Z
	?IsMousePosValid@ImGui@@YA_NPEBUImVec2@@@Z
	?IsMouseReleased@ImGui@@YA_NH@Z
	?IsMouseReleased@ImGui@@YA_NHI@Z
	?IsNamedKey@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsNamedKeyOrModKey@ImGui@@YA_NW4ImGuiKey@@@Z
	?IsNoTabBar@ImGuiDockNode@@QEBA_NXZ
	?IsNodeHovered@ImNodes@@YA_NPEAH@Z
	?IsNodeSelected@ImNodes@@YA_NH@Z
	?IsPacked@ImFontAtlasCustomRect@@QEBA_NXZ
	?IsPinHovered@ImNodes@@YA_NPEAH@Z
	?IsPopupOpen@ImGui@@YA_NIH@Z
	?IsPopupOpen@ImGui@@YA_NPEBDH@Z
	?IsPreview@ImGuiPayload@@QEBA_NXZ
	?IsRectVisible@ImGui@@YA_NAEBUImVec2@@0@Z
	?IsRectVisible@ImGui@@YA_NAEBUImVec2@@@Z
	?IsRootNode@ImGuiDockNode@@QEBA_NXZ
	?IsSplitNode@ImGuiDockNode@@QEBA_NXZ
	?IsWindowAbove@ImGui@@YA_NPEAUImGuiWindow@@0@Z
	?IsWindowAppearing@ImGui@@YA_NXZ
	?IsWindowChildOf@ImGui@@YA_NPEAUImGuiWindow@@0_N1@Z
	?IsWindowCollapsed@ImGui@@YA_NXZ
	?IsWindowContentHoverable@ImGui@@YA_NPEAUImGuiWindow@@H@Z
	?IsWindowDocked@ImGui@@YA_NXZ
	?IsWindowFocused@ImGui@@YA_NH@Z
	?IsWindowHovered@ImGui@@YA_NH@Z
	?IsWindowNavFocusable@ImGui@@YA_NPEAUImGuiWindow@@@Z
	?IsWindowWithinBeginStackOf@ImGui@@YA_NPEAUImGuiWindow@@0@Z
	?ItemAdd@ImGui@@YA_NAEBUImRect@@IPEBU2@H@Z
	?ItemHoverable@ImGui@@YA_NAEBUImRect@@IH@Z
	?ItemSize@ImGui@@YAXAEBUImRect@@M@Z
	?ItemSize@ImGui@@YAXAEBUImVec2@@M@Z
	?KeepAliveID@ImGui@@YAXI@Z
	?LabelText@ImGui@@YAXPEBD0ZZ
	?LabelTextV@ImGui@@YAXPEBD0PEAD@Z
	?Link@ImNodes@@YAXHHH@Z
	?ListBox@ImGui@@YA_NPEBDPEAHP6APEBDPEAXH@Z2HH@Z
	?ListBox@ImGui@@YA_NPEBDPEAHP6A_NPEAXHPEAPEBD@Z2HH@Z
	?ListBox@ImGui@@YA_NPEBDPEAHQEBQEBDHH@Z
	?LoadCurrentEditorStateFromIniFile@ImNodes@@YAXPEBD@Z
	?LoadCurrentEditorStateFromIniString@ImNodes@@YAXPEBD_K@Z
	?LoadEditorStateFromIniFile@ImNodes@@YAXPEAUImNodesEditorContext@@PEBD@Z
	?LoadEditorStateFromIniString@ImNodes@@YAXPEAUImNodesEditorContext@@PEBD_K@Z
	?LoadIniSettingsFromDisk@ImGui@@YAXPEBD@Z
	?LoadIniSettingsFromMemory@ImGui@@YAXPEBD_K@Z
	?LocalizeGetMsg@ImGui@@YAPEBDW4ImGuiLocKey@@@Z
	?LocalizeRegisterEntries@ImGui@@YAXPEBUImGuiLocEntry@@H@Z
	?LogBegin@ImGui@@YAXW4ImGuiLogType@@H@Z
	?LogButtons@ImGui@@YAXXZ
	?LogFinish@ImGui@@YAXXZ
	?LogRenderedText@ImGui@@YAXPEBUImVec2@@PEBD1@Z
	?LogSetNextTextDecoration@ImGui@@YAXPEBD0@Z
	?LogText@ImGui@@YAXPEBDZZ
	?LogTextV@ImGui@@YAXPEBDPEAD@Z
	?LogToBuffer@ImGui@@YAXH@Z
	?LogToClipboard@ImGui@@YAXH@Z
	?LogToFile@ImGui@@YAXHPEBD@Z
	?LogToTTY@ImGui@@YAXH@Z
	?MarkIniSettingsDirty@ImGui@@YAXPEAUImGuiWindow@@@Z
	?MarkIniSettingsDirty@ImGui@@YAXXZ
	?MarkItemEdited@ImGui@@YAXI@Z
	?MemAlloc@ImGui@@YAPEAX_K@Z
	?MemFree@ImGui@@YAXPEAX@Z
	?MenuBarHeight@ImGuiWindow@@QEBAMXZ
	?MenuBarRect@ImGuiWindow@@QEBA?AUImRect@@XZ
	?MenuItem@ImGui@@YA_NPEBD0PEA_N_N@Z
	?MenuItem@ImGui@@YA_NPEBD0_N1@Z
	?MenuItemEx@ImGui@@YA_NPEBD00_N1@Z
	?Merge@ImDrawListSplitter@@QEAAXPEAUImDrawList@@@Z
	?MiniMap@ImNodes@@YAXMHQ6AXHPEAX@ZQEAX@Z
	?MouseButtonToKey@ImGui@@YA?AW4ImGuiKey@@H@Z
	?NavClearPreferredPosForAxis@ImGui@@YAXW4ImGuiAxis@@@Z
	?NavInitRequestApplyResult@ImGui@@YAXXZ
	?NavInitWindow@ImGui@@YAXPEAUImGuiWindow@@_N@Z
	?NavMoveRequestApplyResult@ImGui@@YAXXZ
	?NavMoveRequestButNoResultYet@ImGui@@YA_NXZ
	?NavMoveRequestCancel@ImGui@@YAXXZ
	?NavMoveRequestForward@ImGui@@YAXHHHH@Z
	?NavMoveRequestResolveWithLastItem@ImGui@@YAXPEAUImGuiNavItemData@@@Z
	?NavMoveRequestResolveWithPastTreeNode@ImGui@@YAXPEAUImGuiNavItemData@@PEAUImGuiNavTreeNodeData@@@Z
	?NavMoveRequestSubmit@ImGui@@YAXHHHH@Z
	?NavMoveRequestTryWrapping@ImGui@@YAXPEAUImGuiWindow@@H@Z
	?NavRestoreHighlightAfterMove@ImGui@@YAXXZ
	?NavUpdateCurrentWindowIsScrollPushableX@ImGui@@YAXXZ
	?NewFrame@ImGui@@YAXXZ
	?NewLine@ImGui@@YAXXZ
	?NextColumn@ImGui@@YAXXZ
	?NumSelectedLinks@ImNodes@@YAHXZ
	?NumSelectedNodes@ImNodes@@YAHXZ
	?OnKeyPressed@ImGuiInputTextState@@QEAAXH@Z
	?OpenPopup@ImGui@@YAXIH@Z
	?OpenPopup@ImGui@@YAXPEBDH@Z
	?OpenPopupEx@ImGui@@YAXIH@Z
	?OpenPopupOnItemClick@ImGui@@YAXPEBDH@Z
	?Overlaps@ImRect@@QEBA_NAEBU1@@Z
	?PassFilter@ImGuiTextFilter@@QEBA_NPEBD0@Z
	?PathArcTo@ImDrawList@@QEAAXAEBUImVec2@@MMMH@Z
	?PathArcToFast@ImDrawList@@QEAAXAEBUImVec2@@MHH@Z
	?PathBezierCubicCurveTo@ImDrawList@@QEAAXAEBUImVec2@@00H@Z
	?PathBezierQuadraticCurveTo@ImDrawList@@QEAAXAEBUImVec2@@0H@Z
	?PathEllipticalArcTo@ImDrawList@@QEAAXAEBUImVec2@@MMMMMH@Z
	?PathFillConvex@ImDrawList@@QEAAXI@Z
	?PathLineTo@ImDrawList@@QEAAXAEBUImVec2@@@Z
	?PathRect@ImDrawList@@QEAAXAEBUImVec2@@0MH@Z
	?PathStroke@ImDrawList@@QEAAXIHM@Z
	?PlotEx@ImGui@@YAHW4ImGuiPlotType@@PEBDP6AMPEAXH@Z2HH1MMAEBUImVec2@@@Z
	?PlotHistogram@ImGui@@YAXPEBDP6AMPEAXH@Z1HH0MMUImVec2@@@Z
	?PlotHistogram@ImGui@@YAXPEBDPEBMHH0MMUImVec2@@H@Z
	?PlotLines@ImGui@@YAXPEBDP6AMPEAXH@Z1HH0MMUImVec2@@@Z
	?PlotLines@ImGui@@YAXPEBDPEBMHH0MMUImVec2@@H@Z
	?PopAttributeFlag@ImNodes@@YAXXZ
	?PopButtonRepeat@ImGui@@YAXXZ
	?PopClipRect@ImDrawList@@QEAAXXZ
	?PopClipRect@ImGui@@YAXXZ
	?PopColorStyle@ImNodes@@YAXXZ
	?PopColumnsBackground@ImGui@@YAXXZ
	?PopFocusScope@ImGui@@YAXXZ
	?PopFont@ImGui@@YAXXZ
	?PopID@ImGui@@YAXXZ
	?PopItemFlag@ImGui@@YAXXZ
	?PopItemWidth@ImGui@@YAXXZ
	?PopStyleColor@ImGui@@YAXH@Z
	?PopStyleVar@ImGui@@YAXH@Z
	?PopStyleVar@ImNodes@@YAXH@Z
	?PopTabStop@ImGui@@YAXXZ
	?PopTextWrapPos@ImGui@@YAXXZ
	?PopTextureID@ImDrawList@@QEAAXXZ
	?PrimQuadUV@ImDrawList@@QEAAXAEBUImVec2@@0000000I@Z
	?PrimRect@ImDrawList@@QEAAXAEBUImVec2@@0I@Z
	?PrimRectUV@ImDrawList@@QEAAXAEBUImVec2@@000I@Z
	?PrimReserve@ImDrawList@@QEAAXHH@Z
	?PrimUnreserve@ImDrawList@@QEAAXHH@Z
	?PrimVtx@ImDrawList@@QEAAXAEBUImVec2@@0I@Z
	?PrimWriteIdx@ImDrawList@@QEAAXG@Z
	?PrimWriteVtx@ImDrawList@@QEAAXAEBUImVec2@@0I@Z
	?ProgressBar@ImGui@@YAXMAEBUImVec2@@PEBD@Z
	?PushAttributeFlag@ImNodes@@YAXH@Z
	?PushButtonRepeat@ImGui@@YAX_N@Z
	?PushClipRect@ImDrawList@@QEAAXAEBUImVec2@@0_N@Z
	?PushClipRect@ImGui@@YAXAEBUImVec2@@0_N@Z
	?PushClipRectFullScreen@ImDrawList@@QEAAXXZ
	?PushColorStyle@ImNodes@@YAXHI@Z
	?PushColumnClipRect@ImGui@@YAXH@Z
	?PushColumnsBackground@ImGui@@YAXXZ
	?PushFocusScope@ImGui@@YAXI@Z
	?PushFont@ImGui@@YAXPEAUImFont@@@Z
	?PushID@ImGui@@YAXH@Z
	?PushID@ImGui@@YAXPEBD0@Z
	?PushID@ImGui@@YAXPEBD@Z
	?PushID@ImGui@@YAXPEBX@Z
	?PushItemFlag@ImGui@@YAXH_N@Z
	?PushItemWidth@ImGui@@YAXM@Z
	?PushMultiItemsWidths@ImGui@@YAXHM@Z
	?PushOverrideID@ImGui@@YAXI@Z
	?PushStyleColor@ImGui@@YAXHAEBUImVec4@@@Z
	?PushStyleColor@ImGui@@YAXHI@Z
	?PushStyleVar@ImGui@@YAXHAEBUImVec2@@@Z
	?PushStyleVar@ImGui@@YAXHM@Z
	?PushStyleVar@ImNodes@@YAXHAEBUImVec2@@@Z
	?PushStyleVar@ImNodes@@YAXHM@Z
	?PushTabStop@ImGui@@YAX_N@Z
	?PushTextWrapPos@ImGui@@YAXM@Z
	?PushTextureID@ImDrawList@@QEAAXPEAX@Z
	?RadioButton@ImGui@@YA_NPEBDPEAHH@Z
	?RadioButton@ImGui@@YA_NPEBD_N@Z
	?Rect@ImGuiDockNode@@QEBA?AUImRect@@XZ
	?Rect@ImGuiWindow@@QEBA?AUImRect@@XZ
	?Remove@?$ImPool@UImGuiTable@@@@QEAAXIH@Z
	?Remove@?$ImPool@UImGuiTable@@@@QEAAXIPEBUImGuiTable@@@Z
	?RemoveContextHook@ImGui@@YAXPEAUImGuiContext@@I@Z
	?RemoveSettingsHandler@ImGui@@YAXPEBD@Z
	?Render@ImGui@@YAXXZ
	?RenderArrow@ImGui@@YAXPEAUImDrawList@@UImVec2@@IHM@Z
	?RenderArrowDockMenu@ImGui@@YAXPEAUImDrawList@@UImVec2@@MI@Z
	?RenderArrowPointingAt@ImGui@@YAXPEAUImDrawList@@UImVec2@@1HI@Z
	?RenderBullet@ImGui@@YAXPEAUImDrawList@@UImVec2@@I@Z
	?RenderChar@ImFont@@QEBAXPEAUImDrawList@@MAEBUImVec2@@IG@Z
	?RenderCheckMark@ImGui@@YAXPEAUImDrawList@@UImVec2@@IM@Z
	?RenderColorRectWithAlphaCheckerboard@ImGui@@YAXPEAUImDrawList@@UImVec2@@1IM1MH@Z
	?RenderDragDropTargetRect@ImGui@@YAXAEBUImRect@@0@Z
	?RenderFrame@ImGui@@YAXUImVec2@@0I_NM@Z
	?RenderFrameBorder@ImGui@@YAXUImVec2@@0M@Z
	?RenderMouseCursor@ImGui@@YAXUImVec2@@MHIII@Z
	?RenderNavHighlight@ImGui@@YAXAEBUImRect@@IH@Z
	?RenderPlatformWindowsDefault@ImGui@@YAXPEAX0@Z
	?RenderRectFilledRangeH@ImGui@@YAXPEAUImDrawList@@AEBUImRect@@IMMM@Z
	?RenderRectFilledWithHole@ImGui@@YAXPEAUImDrawList@@AEBUImRect@@1IM@Z
	?RenderText@ImFont@@QEBAXPEAUImDrawList@@MAEBUImVec2@@IAEBUImVec4@@PEBD3M_N@Z
	?RenderText@ImGui@@YAXUImVec2@@PEBD1_N@Z
	?RenderTextClipped@ImGui@@YAXAEBUImVec2@@0PEBD1PEBU2@0PEBUImRect@@@Z
	?RenderTextClippedEx@ImGui@@YAXPEAUImDrawList@@AEBUImVec2@@1PEBD2PEBU3@1PEBUImRect@@@Z
	?RenderTextEllipsis@ImGui@@YAXPEAUImDrawList@@AEBUImVec2@@1MMPEBD2PEBU3@@Z
	?RenderTextWrapped@ImGui@@YAXUImVec2@@PEBD1M@Z
	?Reserve@?$ImPool@UImGuiDockContextPruneNodeData@@@@QEAAXH@Z
	?Reserve@?$ImSpanAllocator@$05@@QEAAXH_KH@Z
	?Reset@ImGuiListClipperData@@QEAAXPEAUImGuiListClipper@@@Z
	?Reset@ImOptionalIndex@@QEAAXXZ
	?ResetMouseDragDelta@ImGui@@YAXH@Z
	?SameLine@ImGui@@YAXMM@Z
	?SaveCurrentEditorStateToIniFile@ImNodes@@YAXPEBD@Z
	?SaveCurrentEditorStateToIniString@ImNodes@@YAPEBDPEA_K@Z
	?SaveEditorStateToIniFile@ImNodes@@YAXPEBUImNodesEditorContext@@PEBD@Z
	?SaveEditorStateToIniString@ImNodes@@YAPEBDPEBUImNodesEditorContext@@PEA_K@Z
	?SaveIniSettingsToDisk@ImGui@@YAXPEBD@Z
	?SaveIniSettingsToMemory@ImGui@@YAPEBDPEA_K@Z
	?ScaleAllSizes@ImGuiStyle@@QEAAXM@Z
	?ScaleClipRects@ImDrawData@@QEAAXAEBUImVec2@@@Z
	?ScaleWindowsInViewport@ImGui@@YAXPEAUImGuiViewportP@@M@Z
	?ScrollToItem@ImGui@@YAXH@Z
	?ScrollToRect@ImGui@@YAXPEAUImGuiWindow@@AEBUImRect@@H@Z
	?ScrollToRectEx@ImGui@@YA?AUImVec2@@PEAUImGuiWindow@@AEBUImRect@@H@Z
	?Scrollbar@ImGui@@YAXW4ImGuiAxis@@@Z
	?ScrollbarEx@ImGui@@YA_NAEBUImRect@@IW4ImGuiAxis@@PEA_J_J3H@Z
	?SelectAll@ImGuiInputTextCallbackData@@QEAAXXZ
	?SelectAll@ImGuiInputTextState@@QEAAXXZ
	?SelectLink@ImNodes@@YAXH@Z
	?SelectNode@ImNodes@@YAXH@Z
	?Selectable@ImGui@@YA_NPEBDPEA_NHAEBUImVec2@@@Z
	?Selectable@ImGui@@YA_NPEBD_NHAEBUImVec2@@@Z
	?Separator@ImGui@@YAXXZ
	?SeparatorEx@ImGui@@YAXHM@Z
	?SeparatorText@ImGui@@YAXPEBD@Z
	?SeparatorTextEx@ImGui@@YAXIPEBD0M@Z
	?SetActiveID@ImGui@@YAXIPEAUImGuiWindow@@@Z
	?SetActiveIdUsingAllKeyboardKeys@ImGui@@YAXXZ
	?SetAllInt@ImGuiStorage@@QEAAXH@Z
	?SetAllocatorFunctions@ImGui@@YAXP6APEAX_KPEAX@ZP6AX11@Z1@Z
	?SetAppAcceptingEvents@ImGuiIO@@QEAAX_N@Z
	?SetArenaBasePtr@?$ImSpanAllocator@$05@@QEAAXPEAX@Z
	?SetBit@?$ImBitArray@$0CJK@$0A@@@QEAAXH@Z
	?SetBit@?$ImBitArray@$0JK@$0?CAA@@@QEAAXH@Z
	?SetBit@ImBitVector@@QEAAXH@Z
	?SetBit@ImFontGlyphRangesBuilder@@QEAAX_K@Z
	?SetBool@ImGuiStorage@@QEAAXI_N@Z
	?SetCircleTessellationMaxError@ImDrawListSharedData@@QEAAXM@Z
	?SetClipboardText@ImGui@@YAXPEBD@Z
	?SetColorEditOptions@ImGui@@YAXH@Z
	?SetColumnOffset@ImGui@@YAXHM@Z
	?SetColumnWidth@ImGui@@YAXHM@Z
	?SetCurrentChannel@ImDrawListSplitter@@QEAAXPEAUImDrawList@@H@Z
	?SetCurrentContext@ImGui@@YAXPEAUImGuiContext@@@Z
	?SetCurrentContext@ImNodes@@YAXPEAUImNodesContext@@@Z
	?SetCurrentFont@ImGui@@YAXPEAUImFont@@@Z
	?SetCurrentViewport@ImGui@@YAXPEAUImGuiWindow@@PEAUImGuiViewportP@@@Z
	?SetCursorPos@ImGui@@YAXAEBUImVec2@@@Z
	?SetCursorPosX@ImGui@@YAXM@Z
	?SetCursorPosY@ImGui@@YAXM@Z
	?SetCursorScreenPos@ImGui@@YAXAEBUImVec2@@@Z
	?SetDragDropPayload@ImGui@@YA_NPEBDPEBX_KH@Z
	?SetFloat@ImGuiStorage@@QEAAXIM@Z
	?SetFocusID@ImGui@@YAXIPEAUImGuiWindow@@@Z
	?SetGlyphVisible@ImFont@@QEAAXG_N@Z
	?SetHoveredID@ImGui@@YAXI@Z
	?SetImGuiContext@ImNodes@@YAXPEAUImGuiContext@@@Z
	?SetInt@ImGuiStorage@@QEAAXIH@Z
	?SetItemAllowOverlap@ImGui@@YAXXZ
	?SetItemDefaultFocus@ImGui@@YAXXZ
	?SetItemKeyOwner@ImGui@@YAXW4ImGuiKey@@H@Z
	?SetItemTooltip@ImGui@@YAXPEBDZZ
	?SetItemTooltipV@ImGui@@YAXPEBDPEAD@Z
	?SetKeyEventNativeData@ImGuiIO@@QEAAXW4ImGuiKey@@HHH@Z
	?SetKeyOwner@ImGui@@YAXW4ImGuiKey@@IH@Z
	?SetKeyOwnersForKeyChord@ImGui@@YAXHIH@Z
	?SetKeyboardFocusHere@ImGui@@YAXH@Z
	?SetLastItemData@ImGui@@YAXIHHAEBUImRect@@@Z
	?SetLocalFlags@ImGuiDockNode@@QEAAXH@Z
	?SetMouseCursor@ImGui@@YAXH@Z
	?SetNavFocusScope@ImGui@@YAXI@Z
	?SetNavID@ImGui@@YAXIW4ImGuiNavLayer@@IAEBUImRect@@@Z
	?SetNavWindow@ImGui@@YAXPEAUImGuiWindow@@@Z
	?SetNextFrameWantCaptureKeyboard@ImGui@@YAX_N@Z
	?SetNextFrameWantCaptureMouse@ImGui@@YAX_N@Z
	?SetNextItemAllowOverlap@ImGui@@YAXXZ
	?SetNextItemOpen@ImGui@@YAX_NH@Z
	?SetNextItemSelectionUserData@ImGui@@YAX_J@Z
	?SetNextItemWidth@ImGui@@YAXM@Z
	?SetNextWindowBgAlpha@ImGui@@YAXM@Z
	?SetNextWindowClass@ImGui@@YAXPEBUImGuiWindowClass@@@Z
	?SetNextWindowCollapsed@ImGui@@YAX_NH@Z
	?SetNextWindowContentSize@ImGui@@YAXAEBUImVec2@@@Z
	?SetNextWindowDockID@ImGui@@YAXIH@Z
	?SetNextWindowFocus@ImGui@@YAXXZ
	?SetNextWindowPos@ImGui@@YAXAEBUImVec2@@H0@Z
	?SetNextWindowScroll@ImGui@@YAXAEBUImVec2@@@Z
	?SetNextWindowSize@ImGui@@YAXAEBUImVec2@@H@Z
	?SetNextWindowSizeConstraints@ImGui@@YAXAEBUImVec2@@0P6AXPEAUImGuiSizeCallbackData@@@ZPEAX@Z
	?SetNextWindowViewport@ImGui@@YAXI@Z
	?SetNodeDraggable@ImNodes@@YAXH_N@Z
	?SetNodeEditorSpacePos@ImNodes@@YAXHAEBUImVec2@@@Z
	?SetNodeGridSpacePos@ImNodes@@YAXHAEBUImVec2@@@Z
	?SetNodeScreenSpacePos@ImNodes@@YAXHAEBUImVec2@@@Z
	?SetScrollFromPosX@ImGui@@YAXMM@Z
	?SetScrollFromPosX@ImGui@@YAXPEAUImGuiWindow@@MM@Z
	?SetScrollFromPosY@ImGui@@YAXMM@Z
	?SetScrollFromPosY@ImGui@@YAXPEAUImGuiWindow@@MM@Z
	?SetScrollHereX@ImGui@@YAXM@Z
	?SetScrollHereY@ImGui@@YAXM@Z
	?SetScrollX@ImGui@@YAXM@Z
	?SetScrollX@ImGui@@YAXPEAUImGuiWindow@@M@Z
	?SetScrollY@ImGui@@YAXM@Z
	?SetScrollY@ImGui@@YAXPEAUImGuiWindow@@M@Z
	?SetShortcutRouting@ImGui@@YA_NHIH@Z
	?SetStateStorage@ImGui@@YAXPEAUImGuiStorage@@@Z
	?SetTabItemClosed@ImGui@@YAXPEBD@Z
	?SetTexID@ImFontAtlas@@QEAAXPEAX@Z
	?SetToContextState@ImGuiStackSizes@@QEAAXPEAUImGuiContext@@@Z
	?SetTooltip@ImGui@@YAXPEBDZZ
	?SetTooltipV@ImGui@@YAXPEBDPEAD@Z
	?SetVoidPtr@ImGuiStorage@@QEAAXIPEAX@Z
	?SetWindowClipRectBeforeSetChannel@ImGui@@YAXPEAUImGuiWindow@@AEBUImRect@@@Z
	?SetWindowCollapsed@ImGui@@YAXPEAUImGuiWindow@@_NH@Z
	?SetWindowCollapsed@ImGui@@YAXPEBD_NH@Z
	?SetWindowCollapsed@ImGui@@YAX_NH@Z
	?SetWindowDock@ImGui@@YAXPEAUImGuiWindow@@IH@Z
	?SetWindowFocus@ImGui@@YAXPEBD@Z
	?SetWindowFocus@ImGui@@YAXXZ
	?SetWindowFontScale@ImGui@@YAXM@Z
	?SetWindowHiddenAndSkipItemsForCurrentFrame@ImGui@@YAXPEAUImGuiWindow@@@Z
	?SetWindowHitTestHole@ImGui@@YAXPEAUImGuiWindow@@AEBUImVec2@@1@Z
	?SetWindowPos@ImGui@@YAXAEBUImVec2@@H@Z
	?SetWindowPos@ImGui@@YAXPEAUImGuiWindow@@AEBUImVec2@@H@Z
	?SetWindowPos@ImGui@@YAXPEBDAEBUImVec2@@H@Z
	?SetWindowSize@ImGui@@YAXAEBUImVec2@@H@Z
	?SetWindowSize@ImGui@@YAXPEAUImGuiWindow@@AEBUImVec2@@H@Z
	?SetWindowSize@ImGui@@YAXPEBDAEBUImVec2@@H@Z
	?SetWindowViewport@ImGui@@YAXPEAUImGuiWindow@@PEAUImGuiViewportP@@@Z
	?ShadeVertsLinearColorGradientKeepAlpha@ImGui@@YAXPEAUImDrawList@@HHUImVec2@@1II@Z
	?ShadeVertsLinearUV@ImGui@@YAXPEAUImDrawList@@HHAEBUImVec2@@111_N@Z
	?ShadeVertsTransformPos@ImGui@@YAXPEAUImDrawList@@HHAEBUImVec2@@MM1@Z
	?Shortcut@ImGui@@YA_NHIH@Z
	?ShowAboutWindow@ImGui@@YAXPEA_N@Z
	?ShowDebugLogWindow@ImGui@@YAXPEA_N@Z
	?ShowDemoWindow@ImGui@@YAXPEA_N@Z
	?ShowFontAtlas@ImGui@@YAXPEAUImFontAtlas@@@Z
	?ShowFontSelector@ImGui@@YAXPEBD@Z
	?ShowIDStackToolWindow@ImGui@@YAXPEA_N@Z
	?ShowMetricsWindow@ImGui@@YAXPEA_N@Z
	?ShowStyleEditor@ImGui@@YAXPEAUImGuiStyle@@@Z
	?ShowStyleSelector@ImGui@@YA_NPEBD@Z
	?ShowUserGuide@ImGui@@YAXXZ
	?ShrinkWidths@ImGui@@YAXPEAUImGuiShrinkWidthItem@@HM@Z
	?Shutdown@ImGui@@YAXXZ
	?SliderAngle@ImGui@@YA_NPEBDPEAMMM0H@Z
	?SliderBehavior@ImGui@@YA_NAEBUImRect@@IHPEAXPEBX2PEBDHPEAU2@@Z
	?SliderFloat2@ImGui@@YA_NPEBDQEAMMM0H@Z
	?SliderFloat3@ImGui@@YA_NPEBDQEAMMM0H@Z
	?SliderFloat4@ImGui@@YA_NPEBDQEAMMM0H@Z
	?SliderFloat@ImGui@@YA_NPEBDPEAMMM0H@Z
	?SliderInt2@ImGui@@YA_NPEBDQEAHHH0H@Z
	?SliderInt3@ImGui@@YA_NPEBDQEAHHH0H@Z
	?SliderInt4@ImGui@@YA_NPEBDQEAHHH0H@Z
	?SliderInt@ImGui@@YA_NPEBDPEAHHH0H@Z
	?SliderScalar@ImGui@@YA_NPEBDHPEAXPEBX20H@Z
	?SliderScalarN@ImGui@@YA_NPEBDHPEAXHPEBX20H@Z
	?SmallButton@ImGui@@YA_NPEBD@Z
	?SnapNodeToGrid@ImNodes@@YAXH@Z
	?Spacing@ImGui@@YAXXZ
	?Split@ImDrawListSplitter@@QEAAXPEAUImDrawList@@H@Z
	?SplitterBehavior@ImGui@@YA_NAEBUImRect@@IW4ImGuiAxis@@PEAM2MMMMI@Z
	?StartMouseMovingWindow@ImGui@@YAXPEAUImGuiWindow@@@Z
	?StartMouseMovingWindowOrNode@ImGui@@YAXPEAUImGuiWindow@@PEAUImGuiDockNode@@_N@Z
	?Step@ImGuiListClipper@@QEAA_NXZ
	?Strdup@ExampleAppConsole@@SAPEADPEBD@Z
	?Stricmp@ExampleAppConsole@@SAHPEBD0@Z
	?Strnicmp@ExampleAppConsole@@SAHPEBD0H@Z
	?Strtrim@ExampleAppConsole@@SAXPEAD@Z
	?StyleColorsClassic@ImGui@@YAXPEAUImGuiStyle@@@Z
	?StyleColorsClassic@ImNodes@@YAXPEAUImNodesStyle@@@Z
	?StyleColorsDark@ImGui@@YAXPEAUImGuiStyle@@@Z
	?StyleColorsDark@ImNodes@@YAXPEAUImNodesStyle@@@Z
	?StyleColorsLight@ImGui@@YAXPEAUImGuiStyle@@@Z
	?StyleColorsLight@ImNodes@@YAXPEAUImNodesStyle@@@Z
	?TabBarAddTab@ImGui@@YAXPEAUImGuiTabBar@@HPEAUImGuiWindow@@@Z
	?TabBarCloseTab@ImGui@@YAXPEAUImGuiTabBar@@PEAUImGuiTabItem@@@Z
	?TabBarFindMostRecentlySelectedTabForActiveWindow@ImGui@@YAPEAUImGuiTabItem@@PEAUImGuiTabBar@@@Z
	?TabBarFindTabByID@ImGui@@YAPEAUImGuiTabItem@@PEAUImGuiTabBar@@I@Z
	?TabBarFindTabByOrder@ImGui@@YAPEAUImGuiTabItem@@PEAUImGuiTabBar@@H@Z
	?TabBarGetCurrentTab@ImGui@@YAPEAUImGuiTabItem@@PEAUImGuiTabBar@@@Z
	?TabBarGetTabName@ImGui@@YAPEBDPEAUImGuiTabBar@@PEAUImGuiTabItem@@@Z
	?TabBarGetTabOrder@ImGui@@YAHPEAUImGuiTabBar@@PEAUImGuiTabItem@@@Z
	?TabBarProcessReorder@ImGui@@YA_NPEAUImGuiTabBar@@@Z
	?TabBarQueueFocus@ImGui@@YAXPEAUImGuiTabBar@@PEAUImGuiTabItem@@@Z
	?TabBarQueueReorder@ImGui@@YAXPEAUImGuiTabBar@@PEAUImGuiTabItem@@H@Z
	?TabBarQueueReorderFromMousePos@ImGui@@YAXPEAUImGuiTabBar@@PEAUImGuiTabItem@@UImVec2@@@Z
	?TabBarRemoveTab@ImGui@@YAXPEAUImGuiTabBar@@I@Z
	?TabItemBackground@ImGui@@YAXPEAUImDrawList@@AEBUImRect@@HI@Z
	?TabItemButton@ImGui@@YA_NPEBDH@Z
	?TabItemCalcSize@ImGui@@YA?AUImVec2@@PEAUImGuiWindow@@@Z
	?TabItemCalcSize@ImGui@@YA?AUImVec2@@PEBD_N@Z
	?TabItemEx@ImGui@@YA_NPEAUImGuiTabBar@@PEBDPEA_NHPEAUImGuiWindow@@@Z
	?TabItemLabelAndCloseButton@ImGui@@YAXPEAUImDrawList@@AEBUImRect@@HUImVec2@@PEBDII_NPEA_N5@Z
	?TableAngledHeadersRow@ImGui@@YAXXZ
	?TableAngledHeadersRowEx@ImGui@@YAXMM@Z
	?TableBeginApplyRequests@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableBeginCell@ImGui@@YAXPEAUImGuiTable@@H@Z
	?TableBeginContextMenuPopup@ImGui@@YA_NPEAUImGuiTable@@@Z
	?TableBeginInitMemory@ImGui@@YAXPEAUImGuiTable@@H@Z
	?TableBeginRow@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableDrawBorders@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableDrawDefaultContextMenu@ImGui@@YAXPEAUImGuiTable@@H@Z
	?TableEndCell@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableEndRow@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableFindByID@ImGui@@YAPEAUImGuiTable@@I@Z
	?TableFixColumnSortDirection@ImGui@@YAXPEAUImGuiTable@@PEAUImGuiTableColumn@@@Z
	?TableFixFlags@@YAHHPEAUImGuiWindow@@@Z
	?TableGcCompactSettings@ImGui@@YAXXZ
	?TableGcCompactTransientBuffers@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableGcCompactTransientBuffers@ImGui@@YAXPEAUImGuiTableTempData@@@Z
	?TableGetBoundSettings@ImGui@@YAPEAUImGuiTableSettings@@PEAUImGuiTable@@@Z
	?TableGetCellBgRect@ImGui@@YA?AUImRect@@PEBUImGuiTable@@H@Z
	?TableGetColumnCount@ImGui@@YAHXZ
	?TableGetColumnFlags@ImGui@@YAHH@Z
	?TableGetColumnIndex@ImGui@@YAHXZ
	?TableGetColumnName@ImGui@@YAPEBDH@Z
	?TableGetColumnName@ImGui@@YAPEBDPEBUImGuiTable@@H@Z
	?TableGetColumnNextSortDirection@ImGui@@YAHPEAUImGuiTableColumn@@@Z
	?TableGetColumnResizeID@ImGui@@YAIPEAUImGuiTable@@HH@Z
	?TableGetColumnWidthAuto@ImGui@@YAMPEAUImGuiTable@@PEAUImGuiTableColumn@@@Z
	?TableGetHeaderAngledMaxLabelWidth@ImGui@@YAMXZ
	?TableGetHeaderRowHeight@ImGui@@YAMXZ
	?TableGetHoveredColumn@ImGui@@YAHXZ
	?TableGetHoveredRow@ImGui@@YAHXZ
	?TableGetInstanceData@ImGui@@YAPEAUImGuiTableInstanceData@@PEAUImGuiTable@@H@Z
	?TableGetInstanceID@ImGui@@YAIPEAUImGuiTable@@H@Z
	?TableGetMaxColumnWidth@ImGui@@YAMPEBUImGuiTable@@H@Z
	?TableGetRowIndex@ImGui@@YAHXZ
	?TableGetSortSpecs@ImGui@@YAPEAUImGuiTableSortSpecs@@XZ
	?TableHeader@ImGui@@YAXPEBD@Z
	?TableHeadersRow@ImGui@@YAXXZ
	?TableLoadSettings@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableMergeDrawChannels@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableNextColumn@ImGui@@YA_NXZ
	?TableNextRow@ImGui@@YAXHM@Z
	?TableOpenContextMenu@ImGui@@YAXH@Z
	?TablePopBackgroundChannel@ImGui@@YAXXZ
	?TablePushBackgroundChannel@ImGui@@YAXXZ
	?TableRemove@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableResetSettings@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableSaveSettings@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableSetBgColor@ImGui@@YAXHIH@Z
	?TableSetColumnEnabled@ImGui@@YAXH_N@Z
	?TableSetColumnIndex@ImGui@@YA_NH@Z
	?TableSetColumnSortDirection@ImGui@@YAXHH_N@Z
	?TableSetColumnWidth@ImGui@@YAXHM@Z
	?TableSetColumnWidthAutoAll@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableSetColumnWidthAutoSingle@ImGui@@YAXPEAUImGuiTable@@H@Z
	?TableSettingsAddSettingsHandler@ImGui@@YAXXZ
	?TableSettingsCreate@ImGui@@YAPEAUImGuiTableSettings@@IH@Z
	?TableSettingsFindByID@ImGui@@YAPEAUImGuiTableSettings@@I@Z
	?TableSetupColumn@ImGui@@YAXPEBDHMI@Z
	?TableSetupDrawChannels@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableSetupScrollFreeze@ImGui@@YAXHH@Z
	?TableSortSpecsBuild@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableSortSpecsSanitize@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableUpdateBorders@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableUpdateColumnsWeightFromWidth@ImGui@@YAXPEAUImGuiTable@@@Z
	?TableUpdateLayout@ImGui@@YAXPEAUImGuiTable@@@Z
	?TeleportMousePos@ImGui@@YAXAEBUImVec2@@@Z
	?TempInputIsActive@ImGui@@YA_NI@Z
	?TempInputScalar@ImGui@@YA_NAEBUImRect@@IPEBDHPEAX1PEBX3@Z
	?TempInputText@ImGui@@YA_NAEBUImRect@@IPEBDPEADHH@Z
	?TestBit@?$ImBitArray@$0CJK@$0A@@@QEBA_NH@Z
	?TestBit@?$ImBitArray@$0JK@$0?CAA@@@QEBA_NH@Z
	?TestBit@ImBitVector@@QEBA_NH@Z
	?TestKeyOwner@ImGui@@YA_NW4ImGuiKey@@I@Z
	?TestShortcutRouting@ImGui@@YA_NHI@Z
	?Text@ImGui@@YAXPEBDZZ
	?TextColored@ImGui@@YAXAEBUImVec4@@PEBDZZ
	?TextColoredV@ImGui@@YAXAEBUImVec4@@PEBDPEAD@Z
	?TextDisabled@ImGui@@YAXPEBDZZ
	?TextDisabledV@ImGui@@YAXPEBDPEAD@Z
	?TextEditCallback@ExampleAppConsole@@QEAAHPEAUImGuiInputTextCallbackData@@@Z
	?TextEditCallbackStub@ExampleAppConsole@@SAHPEAUImGuiInputTextCallbackData@@@Z
	?TextEx@ImGui@@YAXPEBD0H@Z
	?TextUnformatted@ImGui@@YAXPEBD0@Z
	?TextV@ImGui@@YAXPEBDPEAD@Z
	?TextWrapped@ImGui@@YAXPEBDZZ
	?TextWrappedV@ImGui@@YAXPEBDPEAD@Z
	?TitleBarHeight@ImGuiWindow@@QEBAMXZ
	?TitleBarRect@ImGuiWindow@@QEBA?AUImRect@@XZ
	?ToVec4@ImRect@@QEBA?AUImVec4@@XZ
	?Translate@ImRect@@QEAAXAEBUImVec2@@@Z
	?TranslateWindowsInViewport@ImGui@@YAXPEAUImGuiViewportP@@AEBUImVec2@@1@Z
	?TranslateX@ImRect@@QEAAXM@Z
	?TranslateY@ImRect@@QEAAXM@Z
	?TreeNode@ImGui@@YA_NPEBD0ZZ
	?TreeNode@ImGui@@YA_NPEBD@Z
	?TreeNode@ImGui@@YA_NPEBXPEBDZZ
	?TreeNodeBehavior@ImGui@@YA_NIHPEBD0@Z
	?TreeNodeEx@ImGui@@YA_NPEBDH0ZZ
	?TreeNodeEx@ImGui@@YA_NPEBDH@Z
	?TreeNodeEx@ImGui@@YA_NPEBXHPEBDZZ
	?TreeNodeExV@ImGui@@YA_NPEBDH0PEAD@Z
	?TreeNodeExV@ImGui@@YA_NPEBXHPEBDPEAD@Z
	?TreeNodeSetOpen@ImGui@@YAXI_N@Z
	?TreeNodeUpdateNextOpen@ImGui@@YA_NIH@Z
	?TreeNodeV@ImGui@@YA_NPEBD0PEAD@Z
	?TreeNodeV@ImGui@@YA_NPEBXPEBDPEAD@Z
	?TreePop@ImGui@@YAXXZ
	?TreePush@ImGui@@YAXPEBD@Z
	?TreePush@ImGui@@YAXPEBX@Z
	?TreePushOverrideID@ImGui@@YAXI@Z
	?TryGetMapData@?$ImPool@UImGuiTabBar@@@@QEAAPEAUImGuiTabBar@@H@Z
	?TryGetMapData@?$ImPool@UImGuiTable@@@@QEAAPEAUImGuiTable@@H@Z
	?TypingSelectFindBestLeadingMatch@ImGui@@YAHPEAUImGuiTypingSelectRequest@@HP6APEBDPEAXH@Z1@Z
	?TypingSelectFindMatch@ImGui@@YAHPEAUImGuiTypingSelectRequest@@HP6APEBDPEAXH@Z1H@Z
	?TypingSelectFindNextSingleCharMatch@ImGui@@YAHPEAUImGuiTypingSelectRequest@@HP6APEBDPEAXH@Z1H@Z
	?Unindent@ImGui@@YAXM@Z
	?Update@ImGuiMenuColumns@@QEAAXM_N@Z
	?UpdateHoveredWindowAndCaptureFlags@ImGui@@YAXXZ
	?UpdateInputEvents@ImGui@@YAX_N@Z
	?UpdateMergedFlags@ImGuiDockNode@@QEAAXXZ
	?UpdateMouseMovingWindowEndFrame@ImGui@@YAXXZ
	?UpdateMouseMovingWindowNewFrame@ImGui@@YAXXZ
	?UpdatePlatformWindows@ImGui@@YAXXZ
	?UpdateWindowParentAndRootLinks@ImGui@@YAXPEAUImGuiWindow@@H0@Z
	?UpdateWorkRect@ImGuiViewportP@@QEAAXXZ
	?VSliderFloat@ImGui@@YA_NPEBDAEBUImVec2@@PEAMMM0H@Z
	?VSliderInt@ImGui@@YA_NPEBDAEBUImVec2@@PEAHHH0H@Z
	?VSliderScalar@ImGui@@YA_NPEBDAEBUImVec2@@HPEAXPEBX30H@Z
	?Value@ImGui@@YAXPEBDH@Z
	?Value@ImGui@@YAXPEBDI@Z
	?Value@ImGui@@YAXPEBDM0@Z
	?Value@ImGui@@YAXPEBD_N@Z
	?Value@ImOptionalIndex@@QEBAHXZ
	?WindowRectAbsToRel@ImGui@@YA?AUImRect@@PEAUImGuiWindow@@AEBU2@@Z
	?WindowRectRelToAbs@ImGui@@YA?AUImRect@@PEAUImGuiWindow@@AEBU2@@Z
	?_Activate_SSO_buffer@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAXXZ
	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
	?_Allocate@_Default_allocate_traits@std@@SAPEAX_K@Z
	?_CalcCircleAutoSegmentCount@ImDrawList@@QEBAHM@Z
	?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBA_K_K@Z
	?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z
	?_ClearFreeMemory@ImDrawList@@QEAAXXZ
	?_Deallocate_for_capacity@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CAXAEAV?$allocator@D@2@QEAD_K@Z
	?_Empty@?$_Func_class@X$$V@std@@IEBA_NXZ
	?_Get_first@?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAAAEAV?$allocator@D@2@XZ
	?_Get_first@?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEBAAEBV?$allocator@D@2@XZ
	?_Get_first@?$_Compressed_pair@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@V?$_Vector_val@U?$_Simple_types@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@@2@$00@std@@QEAAAEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@XZ
	?_Getal@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV?$allocator@D@2@XZ
	?_Getal@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEBAAEBV?$allocator@D@2@XZ
	?_Getal@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAAEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@XZ
	?_Getimpl@?$_Func_class@X$$V@std@@AEBAPEAV?$_Func_base@X$$V@2@XZ
	?_Large_mode_engaged@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBA_NXZ
	?_Maklocwcs@std@@YAPEA_WPEB_W@Z
	?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ
	?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ
	?_OnChangedClipRect@ImDrawList@@QEAAXXZ
	?_OnChangedTextureID@ImDrawList@@QEAAXXZ
	?_OnChangedVtxOffset@ImDrawList@@QEAAXXZ
	?_Orphan_all@_Container_base12@std@@QEAAXXZ
	?_Orphan_all_locked_v3@_Container_base12@std@@AEAAXXZ
	?_Orphan_all_unlocked_v3@_Container_base12@std@@AEAAXXZ
	?_PathArcToFastEx@ImDrawList@@QEAAXAEBUImVec2@@MHHH@Z
	?_PathArcToN@ImDrawList@@QEAAXAEBUImVec2@@MMMH@Z
	?_PopUnusedDrawCmd@ImDrawList@@QEAAXXZ
	?_Release@_Basic_container_proxy_ptr12@std@@QEAAXXZ
	?_ResetForNewFrame@ImDrawList@@QEAAXXZ
	?_Swap_proxy_and_iterators@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXAEAV12@@Z
	?_Swap_proxy_and_iterators@_Container_base12@std@@QEAAXAEAU12@@Z
	?_Swap_proxy_and_iterators_locked@_Container_base12@std@@AEAAXAEAU12@@Z
	?_Swap_proxy_and_iterators_unlocked@_Container_base12@std@@AEAAXAEAU12@@Z
	?_Switch_to_buf@_Bxty@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAXXZ
	?_Take_contents@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXAEAV12@@Z
	?_Throw_bad_array_new_length@std@@YAXXZ
	?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ
	?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
	?_TryMergeDrawCmds@ImDrawList@@QEAAXXZ
	?_Unchecked_begin@?$vector@VConfig@osgEarth@@V?$allocator@VConfig@osgEarth@@@std@@@std@@QEAAPEAVConfig@osgEarth@@XZ
	?_Unchecked_end@?$vector@VConfig@osgEarth@@V?$allocator@VConfig@osgEarth@@@std@@@std@@QEAAPEAVConfig@osgEarth@@XZ
	?_Xlen_string@std@@YAXXZ
	?__empty_global_delete@@YAXPEAX@Z
	?__empty_global_delete@@YAXPEAXW4align_val_t@std@@@Z
	?__empty_global_delete@@YAXPEAX_K@Z
	?__empty_global_delete@@YAXPEAX_KW4align_val_t@std@@@Z
	?_grow_capacity@?$ImVector@D@@QEBAHH@Z
	?_grow_capacity@?$ImVector@E@@QEBAHH@Z
	?_grow_capacity@?$ImVector@G@@QEBAHH@Z
	?_grow_capacity@?$ImVector@H@@QEBAHH@Z
	?_grow_capacity@?$ImVector@I@@QEBAHH@Z
	?_grow_capacity@?$ImVector@M@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAD@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAUImDrawList@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAUImFont@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAUImGuiDockNode@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAUImGuiViewport@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAUImGuiViewportP@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAUImGuiWindow@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAUMyDocument@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEAX@@QEBAHH@Z
	?_grow_capacity@?$ImVector@PEBD@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImDrawChannel@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImDrawCmd@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImDrawVert@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImFontAtlasCustomRect@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImFontBuildDstData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImFontBuildSrcData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImFontConfig@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImFontGlyph@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiColorMod@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiContextHook@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiDockContextPruneNodeData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiDockNodeSettings@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiDockRequest@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiFocusScopeData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiGroupData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiInputEvent@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiKeyRoutingData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiListClipperData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiListClipperRange@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiNavTreeNodeData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiOldColumnData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiOldColumns@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiPopupData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiPtrOrIndex@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiSettingsHandler@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiShrinkWidthItem@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiStackLevelInfo@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiStyleMod@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiTabBar@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiTabItem@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiTable@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiTableColumnSortSpecs@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiTableInstanceData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiTableTempData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImGuiWindowStackData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImLinkData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImNodeData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImNodesColElement@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImNodesStyleVarElement@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImPinData@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImVec2@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UImVec4@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@UMyDocument@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@Ustbrp_rect@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@Ustbtt_packedchar@@@@QEBAHH@Z
	?_grow_capacity@?$ImVector@_N@@QEBAHH@Z
	?alloc_chunk@?$ImChunkStream@UImGuiTableSettings@@@@QEAAPEAUImGuiTableSettings@@_K@Z
	?alloc_chunk@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAPEAUImGuiWindowSettings@@_K@Z
	?allocate@?$allocator@D@std@@QEAAPEAD_K@Z
	?allocate@?$allocator@U_Container_proxy@std@@@std@@QEAAPEAU_Container_proxy@2@_K@Z
	?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z
	?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD@Z
	?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z
	?append@ImGuiTextBuffer@@QEAAXPEBD0@Z
	?append@ImGuiTextIndex@@QEAAXPEBDHH@Z
	?appendf@ImGuiTextBuffer@@QEAAXPEBDZZ
	?appendfv@ImGuiTextBuffer@@QEAAXPEBDPEAD@Z
	?asCallback@Callback@osg@@$4PPPPPPPM@7EAAPEAV12@XZ
	?asCallback@Callback@osg@@$4PPPPPPPM@7EBAPEBV12@XZ
	?asCallbackObject@Callback@osg@@$4PPPPPPPM@7EAAPEAVCallbackObject@2@XZ
	?asCallbackObject@Callback@osg@@$4PPPPPPPM@7EBAPEBVCallbackObject@2@XZ
	?assign@?$_Narrow_char_traits@DH@std@@SAXAEADAEBD@Z
	?back@?$ImVector@D@@QEBAAEBDXZ
	?back@?$ImVector@H@@QEAAAEAHXZ
	?back@?$ImVector@I@@QEAAAEAIXZ
	?back@?$ImVector@M@@QEAAAEAMXZ
	?back@?$ImVector@PEAUImFont@@@@QEAAAEAPEAUImFont@@XZ
	?back@?$ImVector@PEAUImGuiWindow@@@@QEAAAEAPEAUImGuiWindow@@XZ
	?back@?$ImVector@PEAX@@QEAAAEAPEAXXZ
	?back@?$ImVector@UImDrawCmd@@@@QEAAAEAUImDrawCmd@@XZ
	?back@?$ImVector@UImDrawCmd@@@@QEBAAEBUImDrawCmd@@XZ
	?back@?$ImVector@UImFontConfig@@@@QEAAAEAUImFontConfig@@XZ
	?back@?$ImVector@UImFontGlyph@@@@QEAAAEAUImFontGlyph@@XZ
	?back@?$ImVector@UImGuiColorMod@@@@QEAAAEAUImGuiColorMod@@XZ
	?back@?$ImVector@UImGuiContextHook@@@@QEAAAEAUImGuiContextHook@@XZ
	?back@?$ImVector@UImGuiFocusScopeData@@@@QEAAAEAUImGuiFocusScopeData@@XZ
	?back@?$ImVector@UImGuiGroupData@@@@QEAAAEAUImGuiGroupData@@XZ
	?back@?$ImVector@UImGuiNavTreeNodeData@@@@QEAAAEAUImGuiNavTreeNodeData@@XZ
	?back@?$ImVector@UImGuiOldColumns@@@@QEAAAEAUImGuiOldColumns@@XZ
	?back@?$ImVector@UImGuiPopupData@@@@QEAAAEAUImGuiPopupData@@XZ
	?back@?$ImVector@UImGuiPtrOrIndex@@@@QEAAAEAUImGuiPtrOrIndex@@XZ
	?back@?$ImVector@UImGuiStyleMod@@@@QEAAAEAUImGuiStyleMod@@XZ
	?back@?$ImVector@UImGuiTabItem@@@@QEAAAEAUImGuiTabItem@@XZ
	?back@?$ImVector@UImGuiWindowStackData@@@@QEAAAEAUImGuiWindowStackData@@XZ
	?back@?$ImVector@UImNodesColElement@@@@QEAAAEAUImNodesColElement@@XZ
	?back@?$ImVector@UImNodesStyleVarElement@@@@QEAAAEAUImNodesStyleVarElement@@XZ
	?back@?$ImVector@UImVec2@@@@QEAAAEAUImVec2@@XZ
	?back@?$ImVector@UImVec4@@@@QEAAAEAUImVec4@@XZ
	?back@?$vector@PEAVCamera@osg@@V?$allocator@PEAVCamera@osg@@@std@@@std@@QEAAAEAPEAVCamera@osg@@XZ
	?begin@?$ImChunkStream@UImGuiTableSettings@@@@QEAAPEAUImGuiTableSettings@@XZ
	?begin@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAPEAUImGuiWindowSettings@@XZ
	?begin@?$ImVector@D@@QEAAPEADXZ
	?begin@?$ImVector@G@@QEAAPEAGXZ
	?begin@?$ImVector@H@@QEAAPEAHXZ
	?begin@?$ImVector@I@@QEBAPEBIXZ
	?begin@?$ImVector@PEAD@@QEAAPEAPEADXZ
	?begin@?$ImVector@PEAUImDrawList@@@@QEAAPEAPEAUImDrawList@@XZ
	?begin@?$ImVector@PEAUImFont@@@@QEAAPEAPEAUImFont@@XZ
	?begin@?$ImVector@PEAUImGuiViewportP@@@@QEAAPEAPEAUImGuiViewportP@@XZ
	?begin@?$ImVector@PEAUImGuiWindow@@@@QEAAPEAPEAUImGuiWindow@@XZ
	?begin@?$ImVector@UImDrawCmd@@@@QEAAPEAUImDrawCmd@@XZ
	?begin@?$ImVector@UImDrawCmd@@@@QEBAPEBUImDrawCmd@@XZ
	?begin@?$ImVector@UImFontConfig@@@@QEAAPEAUImFontConfig@@XZ
	?begin@?$ImVector@UImGuiContextHook@@@@QEAAPEAUImGuiContextHook@@XZ
	?begin@?$ImVector@UImGuiDockRequest@@@@QEAAPEAUImGuiDockRequest@@XZ
	?begin@?$ImVector@UImGuiListClipperRange@@@@QEAAPEAUImGuiListClipperRange@@XZ
	?begin@?$ImVector@UImGuiOldColumnData@@@@QEAAPEAUImGuiOldColumnData@@XZ
	?begin@?$ImVector@UImGuiOldColumns@@@@QEAAPEAUImGuiOldColumns@@XZ
	?begin@?$ImVector@UImGuiPlatformMonitor@@@@QEAAPEAUImGuiPlatformMonitor@@XZ
	?begin@?$ImVector@UImGuiPopupData@@@@QEAAPEAUImGuiPopupData@@XZ
	?begin@?$ImVector@UImGuiSettingsHandler@@@@QEAAPEAUImGuiSettingsHandler@@XZ
	?begin@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAAPEAUImGuiStoragePair@ImGuiStorage@@XZ
	?begin@?$ImVector@UImGuiTableTempData@@@@QEAAPEAUImGuiTableTempData@@XZ
	?begin@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEAAPEAUImGuiTextRange@ImGuiTextFilter@@XZ
	?begin@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEBAPEBUImGuiTextRange@ImGuiTextFilter@@XZ
	?begin@?$ImVector@UMyDocument@@@@QEAAPEAUMyDocument@@XZ
	?begin@ImGuiTextBuffer@@QEBAPEBDXZ
	?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ
	?c_str@ImGuiTextBuffer@@QEBAPEBDXZ
	?capacity@?$ImVector@D@@QEBAHXZ
	?chunk_size@?$ImChunkStream@UImGuiTableSettings@@@@QEAAHPEBUImGuiTableSettings@@@Z
	?chunk_size@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAHPEBUImGuiWindowSettings@@@Z
	?className@DrawCallback@Camera@osg@@$4PPPPPPPM@7EBAPEBDXZ
	?clear@?$ImChunkStream@UImGuiTableSettings@@@@QEAAXXZ
	?clear@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAXXZ
	?clear@?$ImVector@D@@QEAAXXZ
	?clear@?$ImVector@E@@QEAAXXZ
	?clear@?$ImVector@G@@QEAAXXZ
	?clear@?$ImVector@H@@QEAAXXZ
	?clear@?$ImVector@I@@QEAAXXZ
	?clear@?$ImVector@M@@QEAAXXZ
	?clear@?$ImVector@PEAD@@QEAAXXZ
	?clear@?$ImVector@PEAUImFont@@@@QEAAXXZ
	?clear@?$ImVector@PEAUImGuiViewportP@@@@QEAAXXZ
	?clear@?$ImVector@PEAUImGuiWindow@@@@QEAAXXZ
	?clear@?$ImVector@PEAUMyDocument@@@@QEAAXXZ
	?clear@?$ImVector@PEAX@@QEAAXXZ
	?clear@?$ImVector@UImDrawChannel@@@@QEAAXXZ
	?clear@?$ImVector@UImDrawCmd@@@@QEAAXXZ
	?clear@?$ImVector@UImDrawVert@@@@QEAAXXZ
	?clear@?$ImVector@UImFontAtlasCustomRect@@@@QEAAXXZ
	?clear@?$ImVector@UImFontBuildDstData@@@@QEAAXXZ
	?clear@?$ImVector@UImFontBuildSrcData@@@@QEAAXXZ
	?clear@?$ImVector@UImFontConfig@@@@QEAAXXZ
	?clear@?$ImVector@UImFontGlyph@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiColorMod@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiDockContextPruneNodeData@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiDockNodeSettings@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiDockRequest@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiGroupData@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiInputEvent@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiKeyRoutingData@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiListClipperData@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiNavTreeNodeData@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiOldColumns@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiPopupData@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiPtrOrIndex@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiSettingsHandler@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiShrinkWidthItem@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiStyleMod@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiTabBar@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiTable@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiTableColumnSortSpecs@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiTableTempData@@@@QEAAXXZ
	?clear@?$ImVector@UImGuiWindowStackData@@@@QEAAXXZ
	?clear@?$ImVector@UImVec2@@@@QEAAXXZ
	?clear@?$ImVector@UImVec4@@@@QEAAXXZ
	?clear@?$ImVector@Ustbrp_rect@@@@QEAAXXZ
	?clear@ImGuiTextBuffer@@QEAAXXZ
	?clear@ImGuiTextIndex@@QEAAXXZ
	?clear_delete@?$ImVector@PEAUImFont@@@@QEAAXXZ
	?clear_delete@?$ImVector@PEAUImGuiViewportP@@@@QEAAXXZ
	?clear_delete@?$ImVector@PEAUImGuiWindow@@@@QEAAXXZ
	?clear_destruct@?$ImVector@UImFontBuildSrcData@@@@QEAAXXZ
	?clear_destruct@?$ImVector@UImGuiListClipperData@@@@QEAAXXZ
	?clear_destruct@?$ImVector@UImGuiOldColumns@@@@QEAAXXZ
	?clear_destruct@?$ImVector@UImGuiTableTempData@@@@QEAAXXZ
	?clone@DrawCallback@Camera@osg@@$4PPPPPPPM@7EBAPEAVObject@3@AEBVCopyOp@3@@Z
	?cloneType@DrawCallback@Camera@osg@@$4PPPPPPPM@7EBAPEAVObject@3@XZ
	?contains@?$ImVector@H@@QEBA_NAEBH@Z
	?contains@?$ImVector@I@@QEBA_NAEBI@Z
	?contains@?$ImVector@PEAUImGuiViewport@@@@QEBA_NAEBQEAUImGuiViewport@@@Z
	?contains@?$ImVector@PEAUImGuiWindow@@@@QEBA_NAEBQEAUImGuiWindow@@@Z
	?convertKey@ImGuiEventHandler@osgEarth@@AEAA?AW4ImGuiKey@@H@Z
	?copy@?$_Char_traits@DH@std@@SAPEADQEADQEBD_K@Z
	?deallocate@?$_Default_allocator_traits@V?$allocator@U_Container_proxy@std@@@std@@@std@@SAXAEAV?$allocator@U_Container_proxy@std@@@2@QEAU_Container_proxy@2@_K@Z
	?deallocate@?$allocator@D@std@@QEAAXQEAD_K@Z
	?deallocate@?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@QEAAXQEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@_K@Z
	?empty@?$ImChunkStream@UImGuiWindowSettings@@@@QEBA_NXZ
	?empty@?$ImVector@D@@QEBA_NXZ
	?empty@?$ImVector@G@@QEBA_NXZ
	?empty@?$ImVector@H@@QEBA_NXZ
	?empty@?$ImVector@I@@QEBA_NXZ
	?empty@?$ImVector@M@@QEBA_NXZ
	?empty@?$ImVector@PEAUImDrawList@@@@QEBA_NXZ
	?empty@?$ImVector@PEAUImFont@@@@QEBA_NXZ
	?empty@?$ImVector@PEAUMyDocument@@@@QEBA_NXZ
	?empty@?$ImVector@UImFontGlyph@@@@QEBA_NXZ
	?empty@?$ImVector@UImGuiPtrOrIndex@@@@QEBA_NXZ
	?empty@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEBA_NXZ
	?empty@?$ImVector@UImGuiWindowStackData@@@@QEBA_NXZ
	?empty@?$ImVector@_N@@QEBA_NXZ
	?empty@?$vector@PEAVCamera@osg@@V?$allocator@PEAVCamera@osg@@@std@@@std@@QEBA_NXZ
	?empty@ImGuiTextBuffer@@QEBA_NXZ
	?empty@ImGuiTextRange@ImGuiTextFilter@@QEBA_NXZ
	?end@?$ImChunkStream@UImGuiTableSettings@@@@QEAAPEAUImGuiTableSettings@@XZ
	?end@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAPEAUImGuiWindowSettings@@XZ
	?end@?$ImVector@G@@QEAAPEAGXZ
	?end@?$ImVector@H@@QEAAPEAHXZ
	?end@?$ImVector@I@@QEBAPEBIXZ
	?end@?$ImVector@PEAD@@QEAAPEAPEADXZ
	?end@?$ImVector@PEAUImDrawList@@@@QEAAPEAPEAUImDrawList@@XZ
	?end@?$ImVector@PEAUImFont@@@@QEAAPEAPEAUImFont@@XZ
	?end@?$ImVector@PEAUImGuiViewportP@@@@QEAAPEAPEAUImGuiViewportP@@XZ
	?end@?$ImVector@PEAUImGuiWindow@@@@QEAAPEAPEAUImGuiWindow@@XZ
	?end@?$ImVector@UImDrawCmd@@@@QEAAPEAUImDrawCmd@@XZ
	?end@?$ImVector@UImFontConfig@@@@QEAAPEAUImFontConfig@@XZ
	?end@?$ImVector@UImGuiContextHook@@@@QEAAPEAUImGuiContextHook@@XZ
	?end@?$ImVector@UImGuiDockRequest@@@@QEAAPEAUImGuiDockRequest@@XZ
	?end@?$ImVector@UImGuiListClipperRange@@@@QEAAPEAUImGuiListClipperRange@@XZ
	?end@?$ImVector@UImGuiOldColumnData@@@@QEAAPEAUImGuiOldColumnData@@XZ
	?end@?$ImVector@UImGuiOldColumns@@@@QEAAPEAUImGuiOldColumns@@XZ
	?end@?$ImVector@UImGuiPlatformMonitor@@@@QEAAPEAUImGuiPlatformMonitor@@XZ
	?end@?$ImVector@UImGuiPopupData@@@@QEAAPEAUImGuiPopupData@@XZ
	?end@?$ImVector@UImGuiSettingsHandler@@@@QEAAPEAUImGuiSettingsHandler@@XZ
	?end@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAAPEAUImGuiStoragePair@ImGuiStorage@@XZ
	?end@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEBAPEBUImGuiStoragePair@ImGuiStorage@@XZ
	?end@?$ImVector@UImGuiTableTempData@@@@QEAAPEAUImGuiTableTempData@@XZ
	?end@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEAAPEAUImGuiTextRange@ImGuiTextFilter@@XZ
	?end@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEBAPEBUImGuiTextRange@ImGuiTextFilter@@XZ
	?end@?$ImVector@UMyDocument@@@@QEAAPEAUMyDocument@@XZ
	?end@ImGuiTextBuffer@@QEBAPEBDXZ
	?eof@?$_Narrow_char_traits@DH@std@@SAHXZ
	?eq_int_type@?$_Narrow_char_traits@DH@std@@SA_NHH@Z
	?equivalent@osg@@YA_NNNN@Z
	?erase@?$ImVector@H@@QEAAPEAHPEBH@Z
	?erase@?$ImVector@PEAD@@QEAAPEAPEADPEBQEAD@Z
	?erase@?$ImVector@PEAUImGuiViewportP@@@@QEAAPEAPEAUImGuiViewportP@@PEBQEAU2@@Z
	?erase@?$ImVector@PEAUImGuiWindow@@@@QEAAPEAPEAUImGuiWindow@@PEBQEAU2@@Z
	?erase@?$ImVector@UImDrawCmd@@@@QEAAPEAUImDrawCmd@@PEBU2@@Z
	?erase@?$ImVector@UImGuiContextHook@@@@QEAAPEAUImGuiContextHook@@PEBU2@@Z
	?erase@?$ImVector@UImGuiInputEvent@@@@QEAAPEAUImGuiInputEvent@@PEBU2@0@Z
	?erase@?$ImVector@UImGuiListClipperRange@@@@QEAAPEAUImGuiListClipperRange@@PEBU2@@Z
	?erase@?$ImVector@UImGuiSettingsHandler@@@@QEAAPEAUImGuiSettingsHandler@@PEBU2@@Z
	?erase@?$ImVector@UImGuiTabItem@@@@QEAAPEAUImGuiTabItem@@PEBU2@@Z
	?erase_unsorted@?$ImVector@H@@QEAAPEAHPEBH@Z
	?find@?$ImVector@H@@QEAAPEAHAEBH@Z
	?find@?$ImVector@PEAUImGuiWindow@@@@QEAAPEAPEAUImGuiWindow@@AEBQEAU2@@Z
	?find_erase@?$ImVector@PEAUImGuiWindow@@@@QEAA_NAEBQEAUImGuiWindow@@@Z
	?find_erase_unsorted@?$ImVector@H@@QEAA_NAEBH@Z
	?front@?$ImVector@D@@QEBAAEBDXZ
	?get@?$ref_ptr@VCamera@osg@@@osg@@QEBAPEAVCamera@2@XZ
	?getCurrentCamera@RenderInfo@osg@@QEAAPEAVCamera@2@XZ
	?getView@RenderInfo@osg@@QEAAPEAVView@2@XZ
	?get_line_begin@ImGuiTextIndex@@QEAAPEBDPEBDH@Z
	?get_line_end@ImGuiTextIndex@@QEAAPEBDPEBDH@Z
	?handle@ImGuiEventHandler@osgEarth@@UEAA_NAEBVGUIEventAdapter@osgGA@@AEAVGUIActionAdapter@4@@Z
	?handleReadSetting@ImGuiEventHandler@osgEarth@@CAXPEAUImGuiContext@@PEAUImGuiSettingsHandler@@PEAXPEBD@Z
	?handleStartEntry@ImGuiEventHandler@osgEarth@@CAPEAXPEAUImGuiContext@@PEAUImGuiSettingsHandler@@PEBD@Z
	?handleWriteSettings@ImGuiEventHandler@osgEarth@@CAXPEAUImGuiContext@@PEAUImGuiSettingsHandler@@PEAUImGuiTextBuffer@@@Z
	?index_from_ptr@?$ImSpan@UImGuiTableColumn@@@@QEBAHPEBUImGuiTableColumn@@@Z
	?index_from_ptr@?$ImVector@PEAUImGuiWindow@@@@QEBAHPEBQEAUImGuiWindow@@@Z
	?index_from_ptr@?$ImVector@UImGuiOldColumnData@@@@QEBAHPEBUImGuiOldColumnData@@@Z
	?index_from_ptr@?$ImVector@UImGuiTabItem@@@@QEBAHPEBUImGuiTabItem@@@Z
	?insert@?$ImVector@PEAUImGuiWindow@@@@QEAAPEAPEAUImGuiWindow@@PEBQEAU2@AEBQEAU2@@Z
	?insert@?$ImVector@UImDrawCmd@@@@QEAAPEAUImDrawCmd@@PEBU2@AEBU2@@Z
	?insert@?$ImVector@UImGuiListClipperRange@@@@QEAAPEAUImGuiListClipperRange@@PEBU2@AEBU2@@Z
	?insert@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAAPEAUImGuiStoragePair@ImGuiStorage@@PEBU23@AEBU23@@Z
	?installSettingsHandler@ImGuiEventHandler@osgEarth@@AEAAXXZ
	?isSameKindAs@DrawCallback@Camera@osg@@$4PPPPPPPM@7EBA_NPEBVObject@3@@Z
	?length@?$_Narrow_char_traits@DH@std@@SA_KQEBD@Z
	?libraryName@DrawCallback@Camera@osg@@$4PPPPPPPM@7EBAPEBDXZ
	?max_size@?$_Default_allocator_traits@V?$allocator@D@std@@@std@@SA_KAEBV?$allocator@D@2@@Z
	?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ
	?memchr@@YAPEAXPEAXH_K@Z
	?move@?$_Char_traits@DH@std@@SAPEADQEADQEBD_K@Z
	?newFrame@ImGuiEventHandler@osgEarth@@QEAAXAEAVRenderInfo@osg@@@Z
	?next_chunk@?$ImChunkStream@UImGuiTableSettings@@@@QEAAPEAUImGuiTableSettings@@PEAU2@@Z
	?next_chunk@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAPEAUImGuiWindowSettings@@PEAU2@@Z
	?offset_from_ptr@?$ImChunkStream@UImGuiTableSettings@@@@QEAAHPEBUImGuiTableSettings@@@Z
	?offset_from_ptr@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAHPEBUImGuiWindowSettings@@@Z
	?pop_back@?$ImVector@H@@QEAAXXZ
	?pop_back@?$ImVector@I@@QEAAXXZ
	?pop_back@?$ImVector@M@@QEAAXXZ
	?pop_back@?$ImVector@PEAUImFont@@@@QEAAXXZ
	?pop_back@?$ImVector@PEAX@@QEAAXXZ
	?pop_back@?$ImVector@UImDrawCmd@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiColorMod@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiFocusScopeData@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiGroupData@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiNavTreeNodeData@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiPopupData@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiPtrOrIndex@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiStyleMod@@@@QEAAXXZ
	?pop_back@?$ImVector@UImGuiWindowStackData@@@@QEAAXXZ
	?pop_back@?$ImVector@UImNodesColElement@@@@QEAAXXZ
	?pop_back@?$ImVector@UImNodesStyleVarElement@@@@QEAAXXZ
	?pop_back@?$ImVector@UImVec4@@@@QEAAXXZ
	?ptr_from_offset@?$ImChunkStream@UImGuiTableSettings@@@@QEAAPEAUImGuiTableSettings@@H@Z
	?ptr_from_offset@?$ImChunkStream@UImGuiWindowSettings@@@@QEAAPEAUImGuiWindowSettings@@H@Z
	?push_back@?$ImVector@D@@QEAAXAEBD@Z
	?push_back@?$ImVector@G@@QEAAXAEBG@Z
	?push_back@?$ImVector@H@@QEAAXAEBH@Z
	?push_back@?$ImVector@I@@QEAAXAEBI@Z
	?push_back@?$ImVector@M@@QEAAXAEBM@Z
	?push_back@?$ImVector@PEAD@@QEAAXAEBQEAD@Z
	?push_back@?$ImVector@PEAUImDrawList@@@@QEAAXAEBQEAUImDrawList@@@Z
	?push_back@?$ImVector@PEAUImFont@@@@QEAAXAEBQEAUImFont@@@Z
	?push_back@?$ImVector@PEAUImGuiDockNode@@@@QEAAXAEBQEAUImGuiDockNode@@@Z
	?push_back@?$ImVector@PEAUImGuiViewport@@@@QEAAXAEBQEAUImGuiViewport@@@Z
	?push_back@?$ImVector@PEAUImGuiViewportP@@@@QEAAXAEBQEAUImGuiViewportP@@@Z
	?push_back@?$ImVector@PEAUImGuiWindow@@@@QEAAXAEBQEAUImGuiWindow@@@Z
	?push_back@?$ImVector@PEAUMyDocument@@@@QEAAXAEBQEAUMyDocument@@@Z
	?push_back@?$ImVector@PEAX@@QEAAXAEBQEAX@Z
	?push_back@?$ImVector@PEBD@@QEAAXAEBQEBD@Z
	?push_back@?$ImVector@UImDrawCmd@@@@QEAAXAEBUImDrawCmd@@@Z
	?push_back@?$ImVector@UImFontAtlasCustomRect@@@@QEAAXAEBUImFontAtlasCustomRect@@@Z
	?push_back@?$ImVector@UImFontConfig@@@@QEAAXAEBUImFontConfig@@@Z
	?push_back@?$ImVector@UImGuiColorMod@@@@QEAAXAEBUImGuiColorMod@@@Z
	?push_back@?$ImVector@UImGuiContextHook@@@@QEAAXAEBUImGuiContextHook@@@Z
	?push_back@?$ImVector@UImGuiDockNodeSettings@@@@QEAAXAEBUImGuiDockNodeSettings@@@Z
	?push_back@?$ImVector@UImGuiDockRequest@@@@QEAAXAEBUImGuiDockRequest@@@Z
	?push_back@?$ImVector@UImGuiFocusScopeData@@@@QEAAXAEBUImGuiFocusScopeData@@@Z
	?push_back@?$ImVector@UImGuiInputEvent@@@@QEAAXAEBUImGuiInputEvent@@@Z
	?push_back@?$ImVector@UImGuiKeyRoutingData@@@@QEAAXAEBUImGuiKeyRoutingData@@@Z
	?push_back@?$ImVector@UImGuiListClipperRange@@@@QEAAXAEBUImGuiListClipperRange@@@Z
	?push_back@?$ImVector@UImGuiOldColumnData@@@@QEAAXAEBUImGuiOldColumnData@@@Z
	?push_back@?$ImVector@UImGuiOldColumns@@@@QEAAXAEBUImGuiOldColumns@@@Z
	?push_back@?$ImVector@UImGuiPopupData@@@@QEAAXAEBUImGuiPopupData@@@Z
	?push_back@?$ImVector@UImGuiPtrOrIndex@@@@QEAAXAEBUImGuiPtrOrIndex@@@Z
	?push_back@?$ImVector@UImGuiSettingsHandler@@@@QEAAXAEBUImGuiSettingsHandler@@@Z
	?push_back@?$ImVector@UImGuiStyleMod@@@@QEAAXAEBUImGuiStyleMod@@@Z
	?push_back@?$ImVector@UImGuiTabItem@@@@QEAAXAEBUImGuiTabItem@@@Z
	?push_back@?$ImVector@UImGuiTableInstanceData@@@@QEAAXAEBUImGuiTableInstanceData@@@Z
	?push_back@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEAAXAEBUImGuiTextRange@ImGuiTextFilter@@@Z
	?push_back@?$ImVector@UImGuiWindowStackData@@@@QEAAXAEBUImGuiWindowStackData@@@Z
	?push_back@?$ImVector@UImNodesColElement@@@@QEAAXAEBUImNodesColElement@@@Z
	?push_back@?$ImVector@UImNodesStyleVarElement@@@@QEAAXAEBUImNodesStyleVarElement@@@Z
	?push_back@?$ImVector@UImVec2@@@@QEAAXAEBUImVec2@@@Z
	?push_back@?$ImVector@UImVec4@@@@QEAAXAEBUImVec4@@@Z
	?push_back@?$ImVector@UMyDocument@@@@QEAAXAEBUMyDocument@@@Z
	?push_back@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAXD@Z
	?push_front@?$ImVector@PEAUImGuiWindow@@@@QEAAXAEBQEAUImGuiWindow@@@Z
	?push_front@?$ImVector@UImDrawCmd@@@@QEAAXAEBUImDrawCmd@@@Z
	?push_front@?$ImVector@UImGuiListClipperRange@@@@QEAAXAEBUImGuiListClipperRange@@@Z
	?releaseGLObjects@DrawCallback@Camera@osg@@$4PPPPPPPM@7EBAXPEAVState@3@@Z
	?render@ImGuiEventHandler@osgEarth@@QEAAXAEAVRenderInfo@osg@@@Z
	?reserve@?$ImVector@D@@QEAAXH@Z
	?reserve@?$ImVector@E@@QEAAXH@Z
	?reserve@?$ImVector@G@@QEAAXH@Z
	?reserve@?$ImVector@H@@QEAAXH@Z
	?reserve@?$ImVector@I@@QEAAXH@Z
	?reserve@?$ImVector@M@@QEAAXH@Z
	?reserve@?$ImVector@PEAD@@QEAAXH@Z
	?reserve@?$ImVector@PEAUImDrawList@@@@QEAAXH@Z
	?reserve@?$ImVector@PEAUImFont@@@@QEAAXH@Z
	?reserve@?$ImVector@PEAUImGuiDockNode@@@@QEAAXH@Z
	?reserve@?$ImVector@PEAUImGuiViewport@@@@QEAAXH@Z
	?reserve@?$ImVector@PEAUImGuiViewportP@@@@QEAAXH@Z
	?reserve@?$ImVector@PEAUImGuiWindow@@@@QEAAXH@Z
	?reserve@?$ImVector@PEAUMyDocument@@@@QEAAXH@Z
	?reserve@?$ImVector@PEAX@@QEAAXH@Z
	?reserve@?$ImVector@PEBD@@QEAAXH@Z
	?reserve@?$ImVector@UImDrawChannel@@@@QEAAXH@Z
	?reserve@?$ImVector@UImDrawCmd@@@@QEAAXH@Z
	?reserve@?$ImVector@UImDrawVert@@@@QEAAXH@Z
	?reserve@?$ImVector@UImFontAtlasCustomRect@@@@QEAAXH@Z
	?reserve@?$ImVector@UImFontBuildDstData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImFontBuildSrcData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImFontConfig@@@@QEAAXH@Z
	?reserve@?$ImVector@UImFontGlyph@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiColorMod@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiContextHook@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiDockContextPruneNodeData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiDockNodeSettings@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiDockRequest@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiFocusScopeData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiGroupData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiInputEvent@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiKeyRoutingData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiListClipperData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiListClipperRange@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiNavTreeNodeData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiOldColumnData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiOldColumns@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiPopupData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiPtrOrIndex@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiSettingsHandler@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiShrinkWidthItem@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiStackLevelInfo@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiStyleMod@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiTabBar@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiTabItem@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiTable@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiTableColumnSortSpecs@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiTableInstanceData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiTableTempData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEAAXH@Z
	?reserve@?$ImVector@UImGuiWindowStackData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImLinkData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImNodeData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImNodesColElement@@@@QEAAXH@Z
	?reserve@?$ImVector@UImNodesStyleVarElement@@@@QEAAXH@Z
	?reserve@?$ImVector@UImPinData@@@@QEAAXH@Z
	?reserve@?$ImVector@UImVec2@@@@QEAAXH@Z
	?reserve@?$ImVector@UImVec4@@@@QEAAXH@Z
	?reserve@?$ImVector@UMyDocument@@@@QEAAXH@Z
	?reserve@?$ImVector@Ustbrp_rect@@@@QEAAXH@Z
	?reserve@?$ImVector@Ustbtt_packedchar@@@@QEAAXH@Z
	?reserve@?$ImVector@_N@@QEAAXH@Z
	?reserve@ImGuiTextBuffer@@QEAAXH@Z
	?reserve_discard@?$ImVector@D@@QEAAXH@Z
	?reserve_discard@?$ImVector@UImVec2@@@@QEAAXH@Z
	?resize@?$ImVector@D@@QEAAXH@Z
	?resize@?$ImVector@D@@QEAAXHAEBD@Z
	?resize@?$ImVector@E@@QEAAXH@Z
	?resize@?$ImVector@G@@QEAAXH@Z
	?resize@?$ImVector@G@@QEAAXHAEBG@Z
	?resize@?$ImVector@H@@QEAAXH@Z
	?resize@?$ImVector@I@@QEAAXH@Z
	?resize@?$ImVector@M@@QEAAXH@Z
	?resize@?$ImVector@M@@QEAAXHAEBM@Z
	?resize@?$ImVector@PEAUImDrawList@@@@QEAAXH@Z
	?resize@?$ImVector@PEAUImGuiViewport@@@@QEAAXH@Z
	?resize@?$ImVector@PEAUImGuiViewportP@@@@QEAAXH@Z
	?resize@?$ImVector@PEAUImGuiWindow@@@@QEAAXH@Z
	?resize@?$ImVector@PEAX@@QEAAXH@Z
	?resize@?$ImVector@UImDrawChannel@@@@QEAAXH@Z
	?resize@?$ImVector@UImDrawCmd@@@@QEAAXH@Z
	?resize@?$ImVector@UImDrawVert@@@@QEAAXH@Z
	?resize@?$ImVector@UImFontBuildDstData@@@@QEAAXH@Z
	?resize@?$ImVector@UImFontBuildSrcData@@@@QEAAXH@Z
	?resize@?$ImVector@UImFontGlyph@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiDockContextPruneNodeData@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiDockNodeSettings@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiDockRequest@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiFocusScopeData@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiGroupData@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiInputEvent@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiKeyRoutingData@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiListClipperData@@@@QEAAXHAEBUImGuiListClipperData@@@Z
	?resize@?$ImVector@UImGuiListClipperRange@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiNavTreeNodeData@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiOldColumnData@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiPopupData@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiShrinkWidthItem@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiStackLevelInfo@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiStackLevelInfo@@@@QEAAXHAEBUImGuiStackLevelInfo@@@Z
	?resize@?$ImVector@UImGuiTabBar@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiTabItem@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiTable@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiTableColumnSortSpecs@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiTableTempData@@@@QEAAXHAEBUImGuiTableTempData@@@Z
	?resize@?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@QEAAXH@Z
	?resize@?$ImVector@UImGuiWindowStackData@@@@QEAAXH@Z
	?resize@?$ImVector@UImLinkData@@@@QEAAXH@Z
	?resize@?$ImVector@UImNodeData@@@@QEAAXH@Z
	?resize@?$ImVector@UImPinData@@@@QEAAXH@Z
	?resize@?$ImVector@UImVec2@@@@QEAAXH@Z
	?resize@?$ImVector@UImVec4@@@@QEAAXH@Z
	?resize@?$ImVector@Ustbrp_rect@@@@QEAAXH@Z
	?resize@?$ImVector@Ustbtt_packedchar@@@@QEAAXH@Z
	?resize@?$ImVector@_N@@QEAAXH@Z
	?resizeGLObjectBuffers@DrawCallback@Camera@osg@@$4PPPPPPPM@7EAAXI@Z
	?select_on_container_copy_construction@?$_Default_allocator_traits@V?$allocator@D@std@@@std@@SA?AV?$allocator@D@2@AEBV32@@Z
	?set@?$ImSpan@F@@QEAAXPEAF0@Z
	?set@?$ImSpan@UImGuiTableCellData@@@@QEAAXPEAUImGuiTableCellData@@0@Z
	?set@?$ImSpan@UImGuiTableColumn@@@@QEAAXPEAUImGuiTableColumn@@0@Z
	?shrink@?$ImVector@G@@QEAAXH@Z
	?shrink@?$ImVector@UImDrawVert@@@@QEAAXH@Z
	?size@?$ImChunkStream@UImGuiTableSettings@@@@QEBAHXZ
	?size@?$ImChunkStream@UImGuiWindowSettings@@@@QEBAHXZ
	?size@?$ImSpan@UImGuiTableColumn@@@@QEBAHXZ
	?size@?$ImVector@D@@QEBAHXZ
	?size@?$ImVector@H@@QEBAHXZ
	?size@?$ImVector@UImLinkData@@@@QEBAHXZ
	?size@?$ImVector@UImNodeData@@@@QEBAHXZ
	?size@?$ImVector@UImNodesColElement@@@@QEBAHXZ
	?size@?$ImVector@UImNodesStyleVarElement@@@@QEBAHXZ
	?size@?$ImVector@UImPinData@@@@QEBAHXZ
	?size@?$ImVector@UImVec2@@@@QEBAHXZ
	?size@?$ImVector@_N@@QEBAHXZ
	?size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ
	?size@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEBA_KXZ
	?size@ImGuiTextBuffer@@QEBAHXZ
	?size@ImGuiTextIndex@@QEAAHXZ
	?size_in_bytes@?$ImVector@PEAUImGuiViewportP@@@@QEBAHXZ
	?size_in_bytes@?$ImVector@UImFontBuildDstData@@@@QEBAHXZ
	?size_in_bytes@?$ImVector@UImFontBuildSrcData@@@@QEBAHXZ
	?size_in_bytes@?$ImVector@UImGuiStoragePair@ImGuiStorage@@@@QEBAHXZ
	?size_in_bytes@?$ImVector@Ustbrp_rect@@@@QEBAHXZ
	?size_in_bytes@?$ImVector@Ustbtt_packedchar@@@@QEBAHXZ
	?size_in_bytes@?$ImVector@_N@@QEBAHXZ
	?split@ImGuiTextRange@ImGuiTextFilter@@QEBAXDPEAU?$ImVector@UImGuiTextRange@ImGuiTextFilter@@@@@Z
	?swap@?$ImChunkStream@UImGuiTableSettings@@@@QEAAXAEAU1@@Z
	?swap@?$ImVector@D@@QEAAXAEAU1@@Z
	?swap@?$ImVector@G@@QEAAXAEAU1@@Z
	?swap@?$ImVector@PEAUImGuiWindow@@@@QEAAXAEAU1@@Z
	?swap@?$ImVector@UImDrawCmd@@@@QEAAXAEAU1@@Z
	?swap@?$ImVector@UImDrawVert@@@@QEAAXAEAU1@@Z
	?swap@?$ImVector@UImGuiKeyRoutingData@@@@QEAAXAEAU1@@Z
	?what@exception@std@@UEBAPEBDXZ
	__local_stdio_printf_options
	__local_stdio_scanf_options
	_snprintf
	_vfprintf_l
	_vsnprintf
	_vsnprintf_l
	_vsprintf_l
	_vsscanf_l
	_vsscanf_s_l
	fabsf
	fprintf
	imgl3wGetProcAddress
	imgl3wInit
	imgl3wInit2
	imgl3wIsSupported
	printf
	sprintf
	sscanf
	sscanf_s
	vsscanf_s
	wmemcpy
