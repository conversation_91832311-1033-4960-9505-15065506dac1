﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4F4ABE29-7C7A-3DD7-802E-657D5B9D0C7E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>osgEarth</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgEarth.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">osgEarthd</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgEarth.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">osgEarth</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgEarth.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">osgEarths</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\bin\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgEarth.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">osgEarth</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /external:I "C:/dev/vcpkg/installed/x64-windows/include/geos" /bigobj /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="Debug";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"Debug\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarthd.dll -installedDir C:/dev/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\debug\lib\osgManipulatord.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgShadowd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgSimd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgViewerd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgGAd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgUtild.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgTextd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgDBd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\OpenThreadsd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\spdlogd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\blosc.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\geos_c.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\blend2d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgManipulatord.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgShadowd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgSimd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgViewerd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgGAd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgUtild.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgTextd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgDBd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\osgd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\OpenThreadsd.lib;opengl32.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\libcurl-d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\fmtd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\lz4d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\zlibd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\zstd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/lib/osgEarthd.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarthd.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /external:I "C:/dev/vcpkg/installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="Release";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"Release\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarth.dll -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\osgManipulator.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgShadow.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgSim.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgViewer.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgGA.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgUtil.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgText.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgDB.lib;C:\dev\vcpkg\installed\x64-windows\lib\osg.lib;C:\dev\vcpkg\installed\x64-windows\lib\OpenThreads.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\blosc.lib;C:\dev\vcpkg\installed\x64-windows\lib\geos_c.lib;C:\dev\vcpkg\installed\x64-windows\lib\blend2d.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgManipulator.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgShadow.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgSim.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgViewer.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgGA.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgUtil.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgText.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgDB.lib;C:\dev\vcpkg\installed\x64-windows\lib\osg.lib;C:\dev\vcpkg\installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcurl.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;C:\dev\vcpkg\installed\x64-windows\lib\lz4.lib;C:\dev\vcpkg\installed\x64-windows\lib\zlib.lib;C:\dev\vcpkg\installed\x64-windows\lib\zstd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/lib/osgEarth.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarth.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /external:I "C:/dev/vcpkg/installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="MinSizeRel";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"MinSizeRel\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarths.dll -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\osgManipulator.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgShadow.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgSim.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgViewer.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgGA.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgUtil.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgText.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgDB.lib;C:\dev\vcpkg\installed\x64-windows\lib\osg.lib;C:\dev\vcpkg\installed\x64-windows\lib\OpenThreads.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\blosc.lib;C:\dev\vcpkg\installed\x64-windows\lib\geos_c.lib;C:\dev\vcpkg\installed\x64-windows\lib\blend2d.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgManipulator.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgShadow.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgSim.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgViewer.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgGA.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgUtil.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgText.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgDB.lib;C:\dev\vcpkg\installed\x64-windows\lib\osg.lib;C:\dev\vcpkg\installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcurl.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;C:\dev\vcpkg\installed\x64-windows\lib\lz4.lib;C:\dev\vcpkg\installed\x64-windows\lib\zlib.lib;C:\dev\vcpkg\installed\x64-windows\lib\zstd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/lib/osgEarths.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarths.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /external:I "C:/dev/vcpkg/installed/x64-windows/include/geos" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="RelWithDebInfo";osgEarth_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_SCL_SECURE_NO_WARNINGS;_CRT_SECURE_NO_DEPRECATE;OSGEARTH_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"RelWithDebInfo\";osgEarth_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src;C:\dev\vcpkg\installed\x64-windows\include;C:\dev\vcpkg\installed\x64-windows\include\geos;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarth.dll -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\osgManipulator.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgShadow.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgSim.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgViewer.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgGA.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgUtil.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgText.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgDB.lib;C:\dev\vcpkg\installed\x64-windows\lib\osg.lib;C:\dev\vcpkg\installed\x64-windows\lib\OpenThreads.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\blosc.lib;C:\dev\vcpkg\installed\x64-windows\lib\geos_c.lib;C:\dev\vcpkg\installed\x64-windows\lib\blend2d.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgManipulator.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgShadow.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgSim.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgViewer.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgGA.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgUtil.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgText.lib;C:\dev\vcpkg\installed\x64-windows\lib\osgDB.lib;C:\dev\vcpkg\installed\x64-windows\lib\osg.lib;C:\dev\vcpkg\installed\x64-windows\lib\OpenThreads.lib;opengl32.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcurl.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;C:\dev\vcpkg\installed\x64-windows\lib\lz4.lib;C:\dev\vcpkg\installed\x64-windows\lib\zlib.lib;C:\dev\vcpkg\installed\x64-windows\lib\zstd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/lib/osgEarth.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/bin/osgEarth.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\a0ce7abc81b0cd44fab22f4ca8e94bcf\AutoGenShaders.cpp.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Generating AutoGenShaders.cpp</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/ConfigureShaders.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CascadeDraping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.Culling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DepthOffset.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Draping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrawInstancedAttribute.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.lib.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HexTiling.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Instancing.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetadataNode.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WireLines.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PhongLighting.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointDrawable.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text_legacy.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ContourMap.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticGraticule.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.VertOnly.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShadowCaster.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimpleOceanLayer.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RTTPicker.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WindLayer.CS.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PBR.glsl;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shaders.cpp.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\AutoGenShaders.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/src/osgEarth/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth -BF:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/osgearth/build_desk/src/osgEarth/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindProtobuf.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindSQLite3.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blend2d\blend2d-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\blosc\blosc-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\libzip\libzip-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Config.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4ConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\lz4\lz4Targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\snappy\SnappyTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\zstd\zstdTargets.cmake;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\cmake\ConfigureShaders.cmake.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in;F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AGG.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AltitudeFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AltitudeSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationRegistry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationSettings">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ArcGISServer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ArcGISTilePackage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AtlasBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AttributesFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AutoClipPlaneHandler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AutoScaleCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AzureMaps">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BboxDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BBoxSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BillboardResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BillboardSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Bing">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Bounds">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BufferFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildGeometryFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildTextFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Cache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CacheBin">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CachePolicy">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CacheSeed">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Callbacks">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Callouts">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CameraUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Capabilities">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CascadeDrapingDecorator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CentroidFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CesiumIon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CircleNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClampableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClampCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Clamping">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClampingTechnique">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClipSpace">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClusterNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Color">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ColorFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Common">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Composite">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CompositeFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CompressedArray">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CompositeTiledModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Config">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Containers">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ContourMap">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ConvertTypeFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Coverage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CoverageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CoverageSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CropFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CssUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Cube">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CullingUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DateTime">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DateTimeRange">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DebugImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DecalLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DepthOffset">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Draggers">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrapeableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrapingCullSet">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrapingTechnique">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrawInstanced">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\EarthManipulator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ECEF">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Elevation">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationLOD">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationPool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationQuery">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationRanges">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\EllipseNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Ellipsoid">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Endian">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Ephemeris">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ExampleResources">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Export">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Expression">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Extension">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ExtrudeGeometryFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ExtrusionSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FadeEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Feature">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureCursor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureDisplayLayout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureImageRTTLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureModelGraph">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureModelSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureSDFLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureSourceIndexNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureStyleSorter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FileUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Fill">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Filter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FilterContext">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FilteredFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FlatteningLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Formatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FractalElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FrameClock">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GARSGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoCommon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Geoid">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoMath">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Geometry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryClamper">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryCloud">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryCompiler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoPositionNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoPositionNodeAutoScaler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GEOS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeosFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoTransform">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GLSLChunker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GLUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GraticuleLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HeightFieldUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Horizon">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HorizonClipPlane">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HTM">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HTTPClient">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IconResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IconSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageMosaic">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageOverlay">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageOverlayEditor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageToFeatureLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageToHeightFieldConverter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceCloud">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IntersectionPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IOTypes">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\JoinLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\JsonUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LabelNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LandCover">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LandCoverLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LatLongFormatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Layer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LayerReference">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LayerShader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Lighting">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LinearLineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineFunctor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LoadableNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LocalGeometryNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LocalTangentPlane">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Locators">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LODGenerator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogarithmicDepthBuffer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Map">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapboxGLGlyphManager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapboxGLImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapModelChange">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapNodeObserver">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MaterialLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Math">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MBTiles">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeasureTool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MemCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MemoryUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeshConsolidator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeshFlattener">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeshSubdivider">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetadataNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetaTile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Metrics">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MGRSFormatter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MGRSGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelResource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MVT">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\NativeProgramAdapter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\NetworkMonitor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\NodeUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\NoiseTextureFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Notify">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ObjectIDPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ObjectIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\OgrUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\optional">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\OverlayDecorator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PagedNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PatchLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PBRMaterial">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PhongLightingEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Picker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PlaceNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PluginLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointDrawable">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PolygonizeLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PolygonSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PowerlineLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PrimitiveIntersector">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Profile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Progress">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Query">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RadialLineOfSight">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Random">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RectangleNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RefinePolicy">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Registry">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RenderSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ResampleFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Resource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ResourceCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ResourceLibrary">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Revisioning">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RTTPicker">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScaleFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScatterFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SceneGraphCallback">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScreenSpaceLayout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScreenSpaceLayoutCallout">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScreenSpaceLayoutDeclutter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScreenSpaceLayoutImpl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Script">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScriptEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScriptFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SDF">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SelectExtentTool">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Session">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderGenerator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderLoader">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderMerger">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shaders">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shadowing">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimpleOceanLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimplePager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimplexNoise">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimplifyFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Skins">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Sky">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SkyView">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SpatialReference">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StarData">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StateSetCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StateTransition">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Status">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StringUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Stroke">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Style">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StyleSelector">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StyleSheet">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SubstituteModelFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Symbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Tags">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TDTiles">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Terrain">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainConstraintLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainEffect">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainEngineNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainEngineRequirements">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainMeshLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainOptions">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainProfile">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainResources">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainTileModel">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainTileModelFactory">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainTileNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TessellateOperator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Tessellator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextSymbol">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextSymbolizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextureArena">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextureBuffer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TFS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TFSPackager">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Threading">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ThreeDTilesLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileCache">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TiledFeatureModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TiledModelLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileEstimator">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileHandler">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileIndex">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileIndexBuilder">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileKey">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileMesher">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileRasterizer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileSourceElevationLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileSourceImageLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileVisitor">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TimeControl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TimeSeriesImage">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TMS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TMSBackFiller">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TopologyGraph">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TrackNode">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TransformFilter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Units">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\URI">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Utils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\UTMGraticule">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\UTMLabelingEngine">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VerticalDatum">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VideoLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ViewFitter">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Viewpoint">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VirtualProgram">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VisibleLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WFS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WindLayer">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WireLines">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WMS">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XmlUtils">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XYZ">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XYZFeatureSource">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XYZModelLayer">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\rtree.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\weemesh.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\weejobs.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\tinyxml\tinyxml.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\tinyxml\tinystr.h" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include\osgEarth\BuildConfig">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\build_include\osgEarth\Version">
    </None>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AltitudeFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AltitudeSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationData.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationRegistry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationSettings.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AnnotationUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ArcGISServer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ArcGISTilePackage.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AtlasBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AttributesFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AutoClipPlaneHandler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\AzureMaps.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BboxDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BBoxSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BillboardResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BillboardSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Bing.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Bounds.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BufferFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildGeometryFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildTextFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Cache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CacheBin.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CachePolicy.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CacheSeed.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Callouts.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CameraUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Capabilities.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CascadeDrapingDecorator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CentroidFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CesiumIon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CircleNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClampableNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClampCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Clamping.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClampingTechnique.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClipSpace.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ClusterNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Color.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ColorFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Composite.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CompositeFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CompositeTiledModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Compressors.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CompressedArray.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Config.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ContourMap.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ConvertTypeFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CoverageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CoverageSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CropFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CssUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Cube.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CullingUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DateTime.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DateTimeRange.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DebugImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DecalLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DepthOffset.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Draggers.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrapeableNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrapingCullSet.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrapingTechnique.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrawInstanced.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\EarthManipulator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ECEF.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Elevation.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationLOD.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationPool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationQuery.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ElevationRanges.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\EllipseNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Ellipsoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Ephemeris.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ExampleResources.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Expression.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Extension.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ExtrudeGeometryFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ExtrusionSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FadeEffect.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Feature.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureCursor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureDisplayLayout.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureImageRTTLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureModelGraph.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureModelSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureSDFLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureSourceIndexNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FeatureStyleSorter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FileUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Fill.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Filter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FilterContext.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FilteredFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FlatteningLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FractalElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\FrameClock.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GARSGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoData.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Geoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoMath.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Geometry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryClamper.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryCloud.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryCompiler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeometryUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoPositionNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoPositionNodeAutoScaler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GEOS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeosFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoTransform.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GLSLChunker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GLUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GraticuleLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HeightFieldUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Horizon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HorizonClipPlane.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HTM.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HTTPClient.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IconResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IconSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageMosaic.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageOverlay.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageOverlayEditor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageToFeatureLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageToHeightFieldConverter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ImageUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceCloud.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\InstanceSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IntersectionPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\IOTypes.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\JoinLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\JsonUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LabelNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LandCover.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LandCoverLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LatLongFormatter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Layer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LayerShader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Lighting.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LinearLineOfSight.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LocalGeometryNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LocalTangentPlane.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LODGenerator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogarithmicDepthBuffer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Map.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapboxGLGlyphManager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapboxGLImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MapNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MaterialLoader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Math.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MBTiles.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeasureTool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MemCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MemoryUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeshConsolidator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeshFlattener.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MeshSubdivider.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetadataNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetaTile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Metrics.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MGRSFormatter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MGRSGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelResource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ModelSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MVT.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\NetworkMonitor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\NodeUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\NoiseTextureFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Notify.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ObjectIDPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ObjectIndex.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\OgrUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\OverlayDecorator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PagedNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PatchLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PBRMaterial.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PhongLightingEffect.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PlaceNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointDrawable.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PolygonizeLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PolygonSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PowerlineLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PrimitiveIntersector.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Profile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Progress.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Query.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RadialLineOfSight.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Random.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RectangleNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Registry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RenderSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ResampleFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Resource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ResourceCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ResourceLibrary.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Revisioning.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RTTPicker.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScaleFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScatterFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SceneGraphCallback.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScreenSpaceLayout.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScriptEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ScriptFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SDF.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SelectExtentTool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Session.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderGenerator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderLoader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderMerger.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShaderUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShapefileReader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeoJSONReader.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shadowing.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimpleOceanLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimplePager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimplexNoise.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimplifyFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Skins.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Sky.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SkyView.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SpatialReference.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StateSetCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Status.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StringUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Stroke.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Style.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StyleSelector.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\StyleSheet.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SubstituteModelFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Symbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TDTiles.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Terrain.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainConstraintLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainEngineNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainMeshLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainOptions.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainProfile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainResources.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainTileModel.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TerrainTileModelFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TessellateOperator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Tessellator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextSymbol.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextSymbolizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextureArena.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextureBuffer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TextureBufferSerializer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TFS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TFSPackager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Threading.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ThreeDTilesLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileCache.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TiledFeatureModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TiledModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileEstimator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileHandler.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileIndex.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileIndexBuilder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileKey.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileMesher.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileRasterizer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileSourceElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileSourceImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TileVisitor.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TimeControl.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TimeSeriesImage.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TMS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TMSBackFiller.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TopologyGraph.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TrackNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\TransformFilter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Units.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\URI.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Utils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\UTMGraticule.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\UTMLabelingEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VerticalDatum.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VideoLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ViewFitter.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Viewpoint.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VirtualProgram.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\VisibleLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WFS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WindLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WireLines.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WMS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XmlUtils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XYZ.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XYZFeatureSource.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\XYZModelLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\tinyxml\tinyxml.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\tinyxml\tinyxmlerror.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\tinyxml\tinystr.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\tinyxml\tinyxmlparser.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarth\AutoGenShaders.cpp" />
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\CascadeDraping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Chonk.Culling.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DepthOffset.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Draping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\DrawInstancedAttribute.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GPUClamping.lib.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\HexTiling.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Instancing.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LineDrawable.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\MetadataNode.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WireLines.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PhongLighting.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PointDrawable.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Text_legacy.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ContourMap.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\GeodeticGraticule.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\LogDepthBuffer.VertOnly.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\ShadowCaster.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\SimpleOceanLayer.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\RTTPicker.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\WindLayer.CS.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\PBR.glsl">
    </None>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\BuildConfig.in" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Version.in" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarth\Shaders.cpp.in" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\ZERO_CHECK.vcxproj">
      <Project>{4A2C7910-819B-3DF4-9EF6-60A9CC2F4215}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>