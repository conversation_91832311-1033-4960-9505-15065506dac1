/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#pragma once

#include <osgEarth/Export>

namespace osgEarth
{
    struct Version
    {
        int major, minor, patch;

        inline bool lessThan(const Version& rhs) const {
            return major < rhs.major ||
                (major == rhs.major && minor < rhs.minor) ||
                (major == rhs.major && minor == rhs.minor && patch < rhs.patch);
        }
        inline bool lessThan(int in_major, int in_minor, int in_patch) const {
            return lessThan(Version{ in_major, in_minor, in_patch });
        }
        inline bool lessThanOrEqualTo(const Version& rhs) const {
            return major < rhs.major ||
                (major == rhs.major && minor < rhs.minor) ||
                (major == rhs.major && minor == rhs.minor && patch <= rhs.patch);
        }
        inline bool lessThanOrEqualTo(int in_major, int in_minor, int in_patch) const {
            return lessThanOrEqualTo(Version{ in_major, in_minor, in_patch });
        }
        inline bool greaterThan(const Version& rhs) const {
            return major > rhs.major ||
                (major == rhs.major && minor > rhs.minor) ||
                (major == rhs.major && minor == rhs.minor && patch > rhs.patch);
        }
        inline bool greaterThan(int in_major, int in_minor, int in_patch) const {
            return greaterThan(Version{ in_major, in_minor, in_patch });
        }
        inline bool greaterThanOrEqualTo(const Version& rhs) const {
            return major > rhs.major ||
                (major == rhs.major && minor > rhs.minor) ||
                (major == rhs.major && minor == rhs.minor && patch >= rhs.patch);
        }
        inline bool greaterThanOrEqualTo(int in_major, int in_minor, int in_patch) const {
            return greaterThanOrEqualTo(Version{ in_major, in_minor, in_patch });
        }
    };

    OSGEARTH_EXPORT Version parseVersion(const char*);
}

extern "C" {

#define OSGEARTH_MAJOR_VERSION 3
#define OSGEARTH_MINOR_VERSION 7
#define OSGEARTH_PATCH_VERSION 3
#define OSGEARTH_SOVERSION 175

/* Convenience macro that can be used to decide whether a feature is present or not i.e.
 * #if OSGEARTH_MIN_VERSION_REQUIRED(1,4,0)
 *    your code here
 * #endif
 */
#define OSGEARTH_MIN_VERSION_REQUIRED(MAJOR, MINOR, PATCH) ((OSGEARTH_MAJOR_VERSION>MAJOR) || (OSGEARTH_MAJOR_VERSION==MAJOR && (OSGEARTH_MINOR_VERSION>MINOR || (OSGEARTH_MINOR_VERSION==MINOR && OSGEARTH_PATCH_VERSION>=PATCH))))
#define OSGEARTH_VERSION_LESS_THAN(MAJOR, MINOR, PATCH) ((OSGEARTH_MAJOR_VERSION<MAJOR) || (OSGEARTH_MAJOR_VERSION==MAJOR && (OSGEARTH_MINOR_VERSION<MINOR || (OSGEARTH_MINOR_VERSION==MINOR && OSGEARTH_PATCH_VERSION<PATCH))))
#define OSGEARTH_VERSION_LESS_OR_EQUAL(MAJOR, MINOR, PATCH) ((OSGEARTH_MAJOR_VERSION<MAJOR) || (OSGEARTH_MAJOR_VERSION==MAJOR && (OSGEARTH_MINOR_VERSION<MINOR || (OSGEARTH_MINOR_VERSION==MINOR && OSGEARTH_PATCH_VERSION<=PATCH))))
#define OSGEARTH_VERSION_GREATER_THAN(MAJOR, MINOR, PATCH) ((OSGEARTH_MAJOR_VERSION>MAJOR) || (OSGEARTH_MAJOR_VERSION==MAJOR && (OSGEARTH_MINOR_VERSION>MINOR || (OSGEARTH_MINOR_VERSION==MINOR && OSGEARTH_PATCH_VERSION>PATCH))))
#define OSGEARTH_VERSION_GREATER_OR_EQUAL(MAJOR, MINOR, PATCH) ((OSGEARTH_MAJOR_VERSION>MAJOR) || (OSGEARTH_MAJOR_VERSION==MAJOR && (OSGEARTH_MINOR_VERSION>MINOR || (OSGEARTH_MINOR_VERSION==MINOR && OSGEARTH_PATCH_VERSION>=PATCH))))

//! Embeds the git SHA1 hash into the library. This is used for version reporting.
#ifdef OSGEARTH_EMBED_GIT_SHA
    extern OSGEARTH_EXPORT const char* osgEarthGitSHA1();
#endif

//! Returns the osgEarth version number in human-friendly form.
extern OSGEARTH_EXPORT const char* osgEarthGetVersion();

//! Returns the ABI/SO version number in string form.
extern OSGEARTH_EXPORT const char* osgEarthGetSOVersion();

//! Returns the library name "osgEarth".
extern OSGEARTH_EXPORT const char* osgEarthGetLibraryName();

#define OSGEARTH_VERSION_RELEASE OSGEARTH_VERSION_PATCH
#define OSGEARTH_VERSION_REVISION 0
}

