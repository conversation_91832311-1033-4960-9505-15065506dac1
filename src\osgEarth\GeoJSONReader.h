/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/Geometry>
#include <osgEarth/JsonUtils>
#include <string>
#include <vector>

namespace osgEarth
{
    /**
     * Simple GeoJSON reader that doesn't depend on GDAL.
     * Supports basic GeoJSON parsing for Point, LineString, Polygon, and their Multi- variants.
     */
    class OSGEARTH_EXPORT GeoJSONReader
    {
    public:
        GeoJSONReader();
        ~GeoJSONReader();

        /**
         * Parse GeoJSON from a string
         */
        bool parseString(const std::string& jsonString, std::vector<osg::ref_ptr<Feature>>& features);

        /**
         * Parse GeoJSON from a file
         */
        bool parseFile(const std::string& filename, std::vector<osg::ref_ptr<Feature>>& features);

        /**
         * Get the last error message
         */
        const std::string& getLastError() const { return _lastError; }

    private:
        bool parseFeatureCollection(const Json::Value& root, std::vector<osg::ref_ptr<Feature>>& features);
        bool parseFeature(const Json::Value& featureJson, osg::ref_ptr<Feature>& feature);
        Geometry* parseGeometry(const Json::Value& geometryJson);
        
        // Geometry parsing methods
        Geometry* parsePoint(const Json::Value& coordinates);
        Geometry* parseLineString(const Json::Value& coordinates);
        Geometry* parsePolygon(const Json::Value& coordinates);
        Geometry* parseMultiPoint(const Json::Value& coordinates);
        Geometry* parseMultiLineString(const Json::Value& coordinates);
        Geometry* parseMultiPolygon(const Json::Value& coordinates);
        Geometry* parseGeometryCollection(const Json::Value& geometries);

        // Utility methods
        osg::Vec3d parseCoordinate(const Json::Value& coord);
        Ring* parseRing(const Json::Value& ringCoords);
        void parseProperties(const Json::Value& properties, Feature* feature);

        void setError(const std::string& error);

    private:
        std::string _lastError;
    };

} // namespace osgEarth
