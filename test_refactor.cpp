/* osgEarth重构测试程序
 * 测试移除GDAL依赖后的基本功能
 */
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/ElevationLayer>
#include <osgEarth/GeosFeatureSource>
#include <osgEarth/FeatureModelLayer>
#include <osgEarth/XYZ>
#include <osgViewer/Viewer>
#include <osgGA/StateSetManipulator>
#include <osgViewer/ViewerEventHandlers>
#include <iostream>

using namespace osgEarth;

int main(int argc, char** argv)
{
    std::cout << "osgEarth重构测试程序启动..." << std::endl;

    try
    {
        // 创建地图
        Map* map = new Map();

        // 添加谷歌地图XYZ瓦片图层（JPG格式）
        XYZ::Options xyzImageOptions;
        xyzImageOptions.url() = "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
        xyzImageOptions.format() = "jpg";
        ImageLayer* imageLayer = new ImageLayer("Google Satellite", xyzImageOptions);
        map->addLayer(imageLayer);

        // 添加AWS Terrarium高程XYZ瓦片（PNG格式）
        XYZ::Options xyzElevOptions;
        xyzElevOptions.url() = "https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png";
        xyzElevOptions.format() = "png";
        ElevationLayer* elevLayer = new ElevationLayer("AWS Terrarium", xyzElevOptions);
        map->addLayer(elevLayer);

        // 测试GeosFeatureSource（替代OGRFeatureSource）
        GeosFeatureSource::Options geosOptions;
        geosOptions.url() = URI("../data/world.shp");
        geosOptions.format() = "shapefile";
        GeosFeatureSource* featureSource = new GeosFeatureSource(geosOptions);
        
        // 创建矢量图层
        FeatureModelLayer* featureLayer = new FeatureModelLayer();
        featureLayer->setFeatureSource(featureSource);
        featureLayer->setName("World Boundaries");
        map->addLayer(featureLayer);

        // 创建地图节点
        MapNode* mapNode = new MapNode(map);

        // 创建查看器
        osgViewer::Viewer viewer;
        viewer.setSceneData(mapNode);
        viewer.setCameraManipulator(new osgEarth::Util::EarthManipulator());

        // 添加一些有用的事件处理器
        viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));
        viewer.addEventHandler(new osgViewer::StatsHandler());
        viewer.addEventHandler(new osgViewer::WindowSizeHandler());

        std::cout << "地图创建成功！" << std::endl;
        std::cout << "图层信息：" << std::endl;
        std::cout << "- 图像图层: Google卫星图像 (XYZ JPG)" << std::endl;
        std::cout << "- 高程图层: AWS Terrarium (XYZ PNG)" << std::endl;
        std::cout << "- 矢量图层: 世界边界 (GeosFeatureSource)" << std::endl;
        std::cout << "按ESC退出..." << std::endl;

        // 运行查看器
        return viewer.run();
    }
    catch (const std::exception& e)
    {
        std::cerr << "错误: " << e.what() << std::endl;
        return -1;
    }
    catch (...)
    {
        std::cerr << "未知错误" << std::endl;
        return -1;
    }
}
