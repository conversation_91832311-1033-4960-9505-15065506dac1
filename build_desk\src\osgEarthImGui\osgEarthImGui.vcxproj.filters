﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\ImGuiEventHandler.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui_demo.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui_draw.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui_tables.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui_widgets.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui_impl_opengl3.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imnodes.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui_internal.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imgui_impl_opengl3.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imconfig.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\imnodes.h">
      <Filter>Headers</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\Common">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\ImGuiEventHandler">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\ImGuiApp">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\ImGuiPanel">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\AnnotationsGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\CameraGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\ContentBrowserGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\EnvironmentGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\LayersGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\NetworkMonitorGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\NotifyGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\OpenEarthFileGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\LiveCamerasGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\NodeGraphGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\PickerGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\RenderingGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\SceneGraphGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\SearchGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\ShaderGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\SystemGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\TerrainGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\TextureInspectorGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\ViewpointsGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\FeatureEditGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\LifeMapLayerGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\TerrainEditGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\TextureSplattingLayerGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\VegetationLayerGUI">
      <Filter>Headers</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthImGui\CesiumIonGUI">
      <Filter>Headers</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Headers">
      <UniqueIdentifier>{0868847D-2552-3EFB-A109-F78A13838989}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources">
      <UniqueIdentifier>{2F1B0EEA-466D-3710-9F7E-3B8DDA2C03D1}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
