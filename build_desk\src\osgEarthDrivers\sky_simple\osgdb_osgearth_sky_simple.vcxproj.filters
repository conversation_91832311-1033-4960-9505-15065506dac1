﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSkyExtension.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSkyNode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\BrunetonImpl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\eb_atmosphere_model.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\eb_texture_buffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\eb_utility.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\eb_ogl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\eb_shaders.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\src\osgEarthDrivers\sky_simple\AutoGenShaders.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSkyShaders.cpp.in">
      <Filter>Template Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\build_desk\CMakeFiles\8c3c0555969b17b71424bfd2652d67ac\AutoGenShaders.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSkyOptions">
      <Filter>Header Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Atmosphere.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Atmosphere.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Ground.ONeil.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Ground.ONeil.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Moon.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Moon.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.GLES.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Stars.GLES.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Sun.frag.glsl">
      <Filter>Shader Files</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\sky_simple\SimpleSky.Sun.vert.glsl">
      <Filter>Shader Files</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{90A39B65-D29D-3C94-AC0B-DC09770202F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{A1FE94ED-136E-3817-88DB-48371F9CD55A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shader Files">
      <UniqueIdentifier>{22749CC1-2900-3D69-B69E-CD0244498527}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{CAAE6D84-9DFC-3582-BE34-ED46F5C123C9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Template Files">
      <UniqueIdentifier>{D6D214E8-42C0-388A-8249-517009CA03A5}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
