<map name="Google Maps with Graticule" type="geocentric" version="2">

    <!-- 谷歌卫星影像图层 - 使用新的XYZImage标签 -->
    <XYZImage name="google_satellite">
        <url>https://mt.google.com/vt/lyrs=s&amp;x={x}&amp;y={y}&amp;z={z}</url>
        
        <!-- 优化的LOD层级范围 - 支持更高精度 -->
        <min_level>0</min_level> 
        <max_level>20</max_level>  <!-- 提高到20级以获得更高精度 -->
        
        <!-- 基于高度的距离范围配置 -->
        <min_range>0</min_range>
        <max_range>1e10</max_range>
        
        <!-- LOD策略配置 - 根据观察高度自动选择合适的瓦片层级 -->
        <lod_blending>true</lod_blending>
        <min_filter>LINEAR_MIPMAP_LINEAR</min_filter>
        <mag_filter>LINEAR</mag_filter>
        
        <!-- 瓦片加载策略 - 优化高层级瓦片加载 -->
        <loading_policy>
            <mode>preemptive</mode>
            <loading_weight>1.0</loading_weight>
            <priority_offset>0</priority_offset>
            <priority_scale>1.0</priority_scale>
        </loading_policy>
        
        <!-- 高层级瓦片的特殊配置 -->
        <tile_size>256</tile_size>
        
        <!-- 网络和缓存选项 -->
        <options>
            <proxy-host>127.0.0.1</proxy-host>
            <proxy-port>10809</proxy-port>
            <user-agent>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</user-agent>
            
            <!-- 高层级瓦片加载优化 -->
            <max_concurrent_requests>8</max_concurrent_requests>
            <timeout>30</timeout>
            <connect_timeout>10</connect_timeout>
            
            <!-- 缓存策略 - 优先使用缓存以提高高层级瓦片加载速度 -->
            <cache_policy>cache_first</cache_policy>
            <cache_max_age>86400</cache_max_age>
        </options>
    </XYZImage>

    <!-- 国界线矢量图层 - 使用本地 GeoJSON 数据 -->
    <feature_model name="world_boundaries" enabled="true">
        <OGRFeatures>
            <url>china_boundaries.geojson</url>
            <build_spatial_index>true</build_spatial_index>
        </OGRFeatures>
        
        <!-- 国界线样式 -->
        <styles>
            <style type="text/css">
                default {
                    stroke: #ff0000;           /* 黄色边界线 */
                    stroke-width: 15px;         /* 线宽 */
                    stroke-opacity: 0.9;       /* 透明度 */
                    fill: none;                /* 不填充 */
                    altitude-clamping: terrain; /* 贴地显示 */
                }
            </style>
        </styles>
        
        <!-- 渲染选项 -->
        <lighting>false</lighting>
        <render_order>2</render_order>
    </feature_model>

    <!-- Tianditu Elevation 
    <elevation name="tianditu_elevation" driver="xyz">
        <url>http://t0.tianditu.gov.cn/ter_w/wmts?SERVICE=WMTS&amp;REQUEST=GetTile&amp;VERSION=1.0.0&amp;LAYER=ter&amp;STYLE=default&amp;TILEMATRIXSET=w&amp;TILEMATRIX={z}&amp;TILEROW={y}&amp;TILECOL={x}&amp;FORMAT=tiles&amp;tk=YOUR_API_KEY</url>
        <profile>spherical-mercator</profile>
    </elevation>-->

    <!-- 经纬网格线 - 使用明显的颜色和配置 -->
    <GeodeticGraticule name="Geodetic" open="true">
        <color>#fff0006f</color>  <!-- 黄色，半透明 -->
        <line_width>2.0</line_width>  <!-- 粗线条 -->
        <grid_lines>10</grid_lines>  <!-- 网格数量 -->
        <resolutions>10 5 2.5 1.0 0.5</resolutions>  <!-- 多级分辨率 -->
        <grid_lines_visible>true</grid_lines_visible>
        <grid_labels_visible>true</grid_labels_visible>
        <edge_labels_visible>true</edge_labels_visible>
        <text>
            <color>#ffff00ff</color>  <!-- 黄色文字 -->
            <size>16</size>
        </text>
    </GeodeticGraticule>


   <!-- 增强的天空效果配置 -->
    <sky driver="simple">
        <!-- 大气光照效果 -->
        <atmospheric_lighting>true</atmospheric_lighting>
        <exposure>4.0</exposure>
        <daytime_ambient_boost>0.2</daytime_ambient_boost>
        <max_ambient_intensity>0.6</max_ambient_intensity>
        
        <!-- 天体可见性 -->
        <sun_visible>true</sun_visible>
        <moon_visible>true</moon_visible>
        <stars_visible>true</stars_visible>
        <atmosphere_visible>true</atmosphere_visible>
        
        <!-- 质量设置 -->
        <quality>high</quality>
        
        <!-- 时间和光照设置 -->
        <hours>12.0</hours>
        <ambient>0.05</ambient>
        
        <!-- PBR渲染 -->
        <pbr>true</pbr>
    </sky>
    

</map> 