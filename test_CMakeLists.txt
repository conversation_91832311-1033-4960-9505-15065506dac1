cmake_minimum_required(VERSION 3.16)
project(osgearth_refactor_test)

set(CMAKE_CXX_STANDARD 20)

# 设置编译器选项
if(MSVC)
    add_compile_options(/utf-8)
endif()

# 查找依赖
find_package(OpenSceneGraph REQUIRED COMPONENTS osg osgDB osgUtil osgGA osgViewer osgText)
find_package(osgEarth REQUIRED)

# 创建测试可执行文件
add_executable(test_refactor test_refactor.cpp)

# 链接库
target_link_libraries(test_refactor
    ${OPENSCENEGRAPH_LIBRARIES}
    osgEarth
)

# 设置包含目录
target_include_directories(test_refactor PRIVATE
    ${OPENSCENEGRAPH_INCLUDE_DIRS}
    ${OSGEARTH_INCLUDE_DIRS}
)

# 设置输出目录
set_target_properties(test_refactor PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 复制DLL到输出目录（Windows）
if(WIN32)
    add_custom_command(TARGET test_refactor POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        $<TARGET_FILE:osgEarth>
        $<TARGET_FILE_DIR:test_refactor>
    )
endif()
