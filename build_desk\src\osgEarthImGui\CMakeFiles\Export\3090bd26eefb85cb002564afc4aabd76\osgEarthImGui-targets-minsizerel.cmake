#----------------------------------------------------------------
# Generated CMake target import file for configuration "MinSizeRel".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "osgEarth::osgEarthImGui" for configuration "MinSizeRel"
set_property(TARGET osgEarth::osgEarthImGui APPEND PROPERTY IMPORTED_CONFIGURATIONS MINSIZEREL)
set_target_properties(osgEarth::osgEarthImGui PROPERTIES
  IMPORTED_IMPLIB_MINSIZEREL "${_IMPORT_PREFIX}/lib/osgEarthImGuis.lib"
  IMPORTED_LINK_DEPENDENT_LIBRARIES_MINSIZEREL "osgEarth::osgEarth"
  IMPORTED_LOCATION_MINSIZEREL "${_IMPORT_PREFIX}/bin/osgEarthImGuis.dll"
  )

list(APPEND _cmake_import_check_targets osgEarth::osgEarthImGui )
list(APPEND _cmake_import_check_files_for_osgEarth::osgEarthImGui "${_IMPORT_PREFIX}/lib/osgEarthImGuis.lib" "${_IMPORT_PREFIX}/bin/osgEarthImGuis.dll" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
