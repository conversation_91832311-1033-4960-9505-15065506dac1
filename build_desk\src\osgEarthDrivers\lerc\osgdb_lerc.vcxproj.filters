﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\lerc\ReaderWriterLERC.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\BitMask.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\BitStuffer2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\Huffman.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\Lerc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\Lerc_c_api_impl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\Lerc2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\RLE.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\Lerc1Decode\BitStuffer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\third_party\lerc\src\LercLib\Lerc1Decode\CntZImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\osgearth_simple2\osgearth\src\osgEarthDrivers\lerc\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{CAAE6D84-9DFC-3582-BE34-ED46F5C123C9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
